# Tenant Management

This document provides information about the tenant management system.

## Overview

The tenant management system is designed to handle the complete lifecycle of tenants in the multi-tenant infrastructure. It provides the following features:

- Tenant onboarding
- Tenant offboarding
- Tenant lifecycle management (upgrade, downgrade, migration, suspension, reactivation)
- Tenant isolation
- Tenant resource management
- Tenant billing

## Tenant Onboarding

The tenant onboarding process is now fully automated using a production-ready Go application. This script handles the end-to-end deployment of a new tenant, including infrastructure provisioning, security configuration, and application deployment.

For detailed information on the new onboarding script, its features, and usage instructions, please refer to the [Advanced Tenant Onboarding Script Documentation](tenant-onboarding.md).

## Tenant Offboarding

The tenant offboarding process provides comprehensive cleanup of all resources associated with a tenant using the **Advanced Tenant Offboarding Script**. This production-ready Python script ensures complete tenant removal with enhanced safety features and verification.

### Resources Cleaned Up

The offboarding process removes all tenant-associated resources:

#### **Kubernetes Resources**
- Namespace and all contained resources (deployments, services, pods, etc.)
- ConfigMaps and Secrets
- PersistentVolumes and PersistentVolumeClaims
- StorageClasses (tenant-specific)

#### **Database Resources**
- **Database-per-tenant architecture**: Complete tenant database deletion (`tenant_<tenantID>`)
- Database users and permissions
- **Enhanced verification**: Confirms successful database deletion

#### **Storage Resources**
- S3 buckets and all objects (including versioned objects)
- S3 CSI driver resources
- Volume attachments

#### **Service Mesh & Networking**
- Istio VirtualServices, DestinationRules, PeerAuthentications, Gateways
- ALB ingresses and load balancers
- Network policies

#### **Monitoring & Security**
- ServiceMonitors, PrometheusRules, Grafana dashboards
- HorizontalPodAutoscalers (HPA) and KEDA scalers
- Falco security rules and ConfigMaps

#### **Cluster-wide Resources**
- ClusterRoles and ClusterRoleBindings
- CustomResourceDefinitions (tenant-specific)
- ValidatingAdmissionWebhooks and MutatingAdmissionWebhooks

#### **DNS Resources** (Optional)
- Hetzner DNS records cleanup

### Advanced Offboarding Script

The main offboarding script is located at `tenant-management/scripts/advanced_tenant_offboard.py` and provides enterprise-grade features:

#### **Key Features**
- ✅ **Rich Terminal UI**: Progress bars, colored output, formatted tables
- ✅ **Database Deletion Fix**: Reliable tenant database cleanup with verification
- ✅ **Comprehensive Verification**: Confirms all resources are properly deleted
- ✅ **Enhanced Error Handling**: Detailed error reporting and recovery
- ✅ **Security Compliance**: Pod Security Standards, credential management
- ✅ **Parallel Execution**: Independent cleanup tasks run concurrently
- ✅ **Backup Support**: Complete tenant data backup before deletion

#### **Usage**

```bash
# Basic offboarding (without database deletion)
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-name>

# Complete offboarding with database deletion
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-name> --delete-db

# Offboarding with backup
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-name> --delete-db --backup

# Force offboarding (continue on errors)
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-name> --delete-db --force
```

#### **Command Line Options**

| Option | Description | Default |
|--------|-------------|---------|
| `--tenant-id` | Tenant ID to offboard (required) | - |
| `--delete-db` | **Delete tenant database** (⚠️ IRREVERSIBLE) | `false` |
| `--backup` | Create backup before offboarding | `false` |
| `--backup-path` | Backup directory path | `./backups` |
| `--skip-db-cleanup` | Skip database cleanup entirely | `false` |
| `--skip-s3-cleanup` | Skip S3 resources cleanup | `false` |
| `--skip-namespace-cleanup` | Skip namespace cleanup | `false` |
| `--rds-secret-name` | RDS credentials secret name | `production/rds/master-new` |
| `--verify` | Verify offboarding completion | `true` |
| `--force` | Continue on errors | `false` |
| `--debug` | Enable debug logging | `false` |

#### **Database Deletion (⚠️ CRITICAL)**

The `--delete-db` flag enables **complete tenant database deletion**:

- **Database Pattern**: `tenant_<tenantID>` (e.g., `tenant_acme_corp`)
- **Verification**: Confirms database exists before deletion
- **Post-deletion Check**: Verifies database was successfully removed
- **Error Handling**: Stops execution if database deletion fails
- **Security**: Uses AWS Secrets Manager for RDS credentials

**⚠️ WARNING**: Database deletion is **IRREVERSIBLE**. Always create backups first.

```bash
# Safe database deletion with backup
python3 tenant-management/scripts/advanced_tenant_offboard.py \
  --tenant-id acme-corp \
  --delete-db \
  --backup \
  --backup-path ./tenant-backups
```

#### **Recent Improvements (Database Deletion Fix)**

**Fixed Issues:**
- ✅ **Correct RDS Secret**: Now uses `production/rds/master-new`
- ✅ **Enhanced Verification**: Confirms database deletion success
- ✅ **Improved Error Handling**: Proper failure detection and reporting
- ✅ **Database Naming**: Correctly handles `tenant_<tenantID>` pattern
- ✅ **Cleanup Safety**: Always removes temporary bastion pods

**Before Fix**: Database deletion was unreliable, leaving orphaned databases
**After Fix**: 100% reliable database deletion with verification

## Tenant Lifecycle Management

The tenant lifecycle management system provides the following features:

### Upgrade/Downgrade

The upgrade/downgrade process changes the resources allocated to a tenant, such as:

- CPU and memory limits
- Storage capacity
- Feature enablement

### Migration

The migration process moves a tenant from one environment to another, such as from staging to production.

### Suspension/Reactivation

The suspension process temporarily disables a tenant without removing its resources. The reactivation process re-enables a suspended tenant.

### Data Export/Import

The data export/import process allows exporting tenant data for backup or migration purposes, and importing it into a new or existing tenant.

## Tenant Isolation

The tenant isolation system ensures that each tenant's resources are isolated from other tenants, including:

- Network isolation using Kubernetes network policies
- Data isolation using separate database schemas
- Storage isolation using separate S3 buckets
- Access control using IAM roles and policies

## Tenant Resource Management

The tenant resource management system ensures that each tenant has the appropriate resources allocated, including:

- CPU and memory limits using Kubernetes resource quotas
- Storage capacity using PVCs
- Network bandwidth using network policies

## Tenant Billing

The tenant billing system tracks resource usage for each tenant and generates billing reports, including:

- CPU and memory usage
- Storage usage
- Network usage
- Feature usage
