apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.tenant.id }}-webapp-secrets
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
type: Opaque
stringData:
  # AV Automate S3 Credentials
  AV_AUTOMATE_S3_KEY: {{ .Values.secrets.avAutomateS3Key | default "5aef88a01e400845a837" | quote }}
  AV_AUTOMATE_S3_SECRET_KEY: {{ .Values.secrets.avAutomateS3SecretKey | default "sVOOSYFZai9a0OWzpOQxSY0x48WvmS6ve6C0roYY" | quote }}

  # DCM Keys
  DCM_PRIVATE_KEY: {{ .Values.secrets.dcmPrivateKey | default "0088247850db89e38c2327c1aacdd263a74e2b271a35d2c39e1b9f50e306cebb" | quote }}
  DCM_PUBLIC_KEY: {{ .Values.secrets.dcmPublicKey | default "46fe06e9781a6a61f53cb38dfd9f33ef422f088c3bfcc050ebe0ecede7e6e95b" | quote }}

  # Instance Pre-Shared Key
  INSTANCE_PRE_SHARED_KEY: {{ .Values.secrets.instancePreSharedKey | default "123" | quote }}

  # MySQL Password (will be updated by onboarding script)
  MYSQL_PASSWORD: {{ .Values.secrets.mysqlPassword | default "PLACEHOLDER_PASSWORD" | quote }}

  # RabbitMQ Password
  RABBITMQ_PASSWORD: {{ .Values.secrets.rabbitmqPassword | default "guest" | quote }}

  # Release Notes API Token
  RELEASE_NOTES_API_TOKEN: {{ .Values.secrets.releaseNotesApiToken | default "asdasd" | quote }}
