{{- if .Values.rabbitmq.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.tenant.id }}-rabbitmq
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: rabbitmq
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.service.type }}
  selector:
    {{- include "architrave-tenant.selectorLabels" . | nindent 4 }}
    app: rabbitmq
  ports:
    - name: amqp
      protocol: TCP
      port: {{ .Values.rabbitmq.ports.amqp }}
      targetPort: {{ .Values.rabbitmq.ports.amqp }}
    - name: epmd
      protocol: TCP
      port: {{ .Values.rabbitmq.ports.epmd }}
      targetPort: {{ .Values.rabbitmq.ports.epmd }}
    {{- if .Values.rabbitmq.management.enabled }}
    - name: management
      protocol: TCP
      port: {{ .Values.rabbitmq.ports.management }}
      targetPort: {{ .Values.rabbitmq.ports.management }}
    {{- end }}
{{- end }}
