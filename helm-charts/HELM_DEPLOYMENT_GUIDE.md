# Helm-Based Tenant Deployment Guide

## Overview

The Architrave tenant onboarding system now supports Helm-based deployments as an alternative to individual component deployments. This provides a more streamlined, consistent, and maintainable approach to tenant provisioning.

## Features

### Comprehensive Helm Chart
- **All-in-one deployment**: Single Helm chart deploys all tenant components
- **Modular templates**: Separate templates for different component types
- **Environment-specific values**: Different configurations for development and production
- **Conditional deployment**: Enable/disable components based on configuration

### Components Included
- **Storage**: S3 StorageClass, PersistentVolume, and PersistentVolumeClaim
- **RabbitMQ**: Message queue with management interface
- **Queue Workers**: Default, folder, and notification queue processors
- **CronJobs**: Document processing tasks (every 5, 10, and 15 minutes)
- **Istio**: VirtualService and DestinationRule for traffic management
- **ConfigMaps and Secrets**: Tenant-specific configuration

## Usage

### Onboarding with Helm

#### Using the Go Onboarding Script
```bash
# Enable Helm deployment mode
./advanced_tenant_onboard --tenant-id my-tenant --use-helm

# With production configuration
./advanced_tenant_onboard --tenant-id my-tenant --use-helm --production
```

#### Manual Helm Deployment
```bash
# Deploy using default values
helm install tenant-my-tenant ./helm-charts/architrave-tenant \
  --namespace tenant-my-tenant \
  --create-namespace \
  --set tenant.id=my-tenant \
  --set tenant.domain=my-tenant.architrave-assets.com

# Deploy with production values
helm install tenant-my-tenant ./helm-charts/architrave-tenant \
  --namespace tenant-my-tenant \
  --create-namespace \
  --values ./helm-charts/architrave-tenant/values-production.yaml \
  --set tenant.id=my-tenant \
  --set tenant.domain=my-tenant.architrave-assets.com
```

### Offboarding with Helm

#### Using the Python Offboarding Script
```bash
# Standard offboarding (includes Helm cleanup)
python3 advanced_tenant_offboard.py --tenant-id my-tenant

# With database deletion
python3 advanced_tenant_offboard.py --tenant-id my-tenant --delete-db
```

#### Manual Helm Cleanup
```bash
# Uninstall Helm release
helm uninstall tenant-my-tenant --namespace tenant-my-tenant

# Clean up namespace
kubectl delete namespace tenant-my-tenant
```

## Configuration

### Values Structure
```yaml
tenant:
  id: "my-tenant"
  domain: "my-tenant.architrave-assets.com"

images:
  webapp: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24"
  nginx: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.8-preview-2"
  rabbitmq: "545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02"

resources:
  webapp:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"

storage:
  enabled: true
  s3BucketName: "tenant-my-tenant"

cronjobs:
  processDocuments:
    enabled: true
    schedule: "*/5 * * * *"
  processStaged:
    enabled: true
    schedule: "*/10 * * * *"
  reprocess:
    enabled: true
    schedule: "*/15 * * * *"

queues:
  default:
    enabled: true
    replicas: 2
  folder:
    enabled: true
    replicas: 1
  notification:
    enabled: true
    replicas: 1

rabbitmq:
  enabled: true
  replicas: 1
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "200m"
```

### Environment-Specific Configurations

#### Development (values.yaml)
- Lower resource limits
- Less frequent CronJob schedules
- Single replica deployments
- Basic security settings

#### Production (values-production.yaml)
- Higher resource limits
- More frequent CronJob schedules
- Multiple replica deployments
- Enhanced security configurations
- Resource quotas and limits

## Template Structure

```
helm-charts/architrave-tenant/
├── Chart.yaml                 # Chart metadata
├── values.yaml               # Default values
├── values-production.yaml    # Production values
├── templates/
│   ├── _helpers.tpl          # Template helpers
│   ├── configmap.yaml        # Tenant configuration
│   ├── secret.yaml           # Tenant secrets
│   ├── storage/
│   │   ├── storageclass.yaml # S3 StorageClass
│   │   ├── pv.yaml           # PersistentVolume
│   │   └── pvc.yaml          # PersistentVolumeClaim
│   ├── rabbitmq/
│   │   ├── configmap.yaml    # RabbitMQ config
│   │   ├── deployment.yaml   # RabbitMQ deployment
│   │   └── service.yaml      # RabbitMQ service
│   ├── queues/
│   │   ├── default-queue.yaml      # Default queue worker
│   │   ├── folder-queue.yaml       # Folder queue worker
│   │   └── notification-queue.yaml # Notification queue worker
│   ├── cronjobs/
│   │   ├── process-documents.yaml        # Process documents CronJob
│   │   ├── process-staged-documents.yaml # Process staged CronJob
│   │   └── reprocess-documents.yaml      # Reprocess CronJob
│   └── istio/
│       ├── virtualservice.yaml     # Istio VirtualService
│       └── destinationrule.yaml    # Istio DestinationRule
└── deploy-tenant-helm.sh     # Standalone deployment script
```

## Benefits

### For Operations Teams
- **Consistency**: All tenants deployed with identical configurations
- **Maintainability**: Single source of truth for tenant deployments
- **Rollback capability**: Easy rollback to previous versions
- **Version control**: Track changes to tenant configurations

### For Development Teams
- **Simplified deployment**: Single command deploys all components
- **Environment parity**: Same deployment process across environments
- **Configuration management**: Centralized configuration with overrides
- **Testing**: Easy to test changes in isolated environments

## Migration from Individual Deployments

### Automatic Fallback
The onboarding script includes automatic fallback:
1. Attempts Helm deployment first (if `--use-helm` flag is used)
2. Falls back to individual deployments if Helm fails
3. Logs warnings but continues with standard deployment

### Manual Migration
For existing tenants, you can migrate to Helm management:
1. Export current configuration
2. Create equivalent Helm values
3. Deploy with Helm (will update existing resources)
4. Verify deployment integrity

## Troubleshooting

### Common Issues

#### Helm Not Found
```bash
# Install Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

#### Release Already Exists
```bash
# Check existing releases
helm list -A

# Upgrade existing release
helm upgrade tenant-my-tenant ./helm-charts/architrave-tenant
```

#### Template Validation Errors
```bash
# Validate templates
helm template tenant-my-tenant ./helm-charts/architrave-tenant --debug

# Dry run deployment
helm install tenant-my-tenant ./helm-charts/architrave-tenant --dry-run
```

### Debugging

#### Check Helm Release Status
```bash
helm status tenant-my-tenant --namespace tenant-my-tenant
```

#### View Generated Manifests
```bash
helm get manifest tenant-my-tenant --namespace tenant-my-tenant
```

#### Check Helm Values
```bash
helm get values tenant-my-tenant --namespace tenant-my-tenant
```

## Next Steps

1. **Testing**: Thoroughly test Helm deployments in development
2. **Monitoring**: Add monitoring for Helm-deployed tenants
3. **Automation**: Integrate with CI/CD pipelines
4. **Documentation**: Update operational runbooks
5. **Training**: Train operations team on Helm workflows
