# Helm Integration Testing Procedures

## Overview

This document outlines comprehensive testing procedures for the new Helm-based tenant deployment system. These tests ensure the integration works correctly and provides reliable tenant provisioning.

## Prerequisites

### Required Tools
```bash
# Verify required tools are installed
kubectl version --client
helm version
aws --version
python3 --version
go version

# Verify cluster access
kubectl cluster-info
kubectl get nodes
```

### Environment Setup
```bash
# Set environment variables
export AWS_REGION=eu-central-1
export KUBECONFIG=/path/to/your/kubeconfig
export HETZNER_DNS_TOKEN=your-hetzner-token

# Verify AWS credentials
aws sts get-caller-identity

# Verify He<PERSON> is working
helm list -A
```

## Test Suite 1: Helm Chart Validation

### Test 1.1: Template Validation
```bash
# Test template rendering
cd helm-charts/architrave-tenant

# Validate with default values
helm template test-tenant . --debug

# Validate with production values
helm template test-tenant . --values values-production.yaml --debug

# Check for template errors
helm lint .
```

**Expected Results:**
- No template errors
- All resources render correctly
- Conditional logic works properly

### Test 1.2: Values Validation
```bash
# Test different tenant configurations
helm template test-tenant . \
  --set tenant.id=test-validation \
  --set tenant.domain=test-validation.architrave-assets.com \
  --set storage.enabled=false \
  --debug

# Test production configuration
helm template test-tenant . \
  --values values-production.yaml \
  --set tenant.id=prod-test \
  --debug
```

**Expected Results:**
- Tenant-specific values are correctly substituted
- Conditional resources are enabled/disabled properly
- Production values override defaults correctly

## Test Suite 2: Go Onboarding Script Integration

### Test 2.1: Helm Flag Integration
```bash
# Test help message includes Helm flag
./advanced_tenant_onboard --help | grep -i helm

# Test flag parsing
./advanced_tenant_onboard --tenant-id test-flag --use-helm --dry-run
```

**Expected Results:**
- `--use-helm` flag is documented in help
- Flag is parsed correctly
- Configuration includes UseHelm=true

### Test 2.2: Helm Deployment Path
```bash
# Test Helm deployment (dry run)
./advanced_tenant_onboard \
  --tenant-id helm-test-1 \
  --use-helm \
  --skip-dns \
  --skip-web-check \
  --debug

# Verify Helm release was created
helm list -n tenant-helm-test-1

# Check deployed resources
kubectl get all -n tenant-helm-test-1
```

**Expected Results:**
- Helm chart deploys successfully
- All expected resources are created
- Tenant namespace is properly configured

### Test 2.3: Fallback Mechanism
```bash
# Test fallback when Helm fails (simulate by removing Helm)
sudo mv /usr/local/bin/helm /usr/local/bin/helm.bak

./advanced_tenant_onboard \
  --tenant-id fallback-test \
  --use-helm \
  --skip-dns \
  --skip-web-check \
  --debug

# Restore Helm
sudo mv /usr/local/bin/helm.bak /usr/local/bin/helm
```

**Expected Results:**
- Script detects Helm failure
- Falls back to individual deployments
- Tenant is still deployed successfully

## Test Suite 3: Python Offboarding Script Integration

### Test 3.1: Helm Cleanup Detection
```bash
# Deploy a tenant with Helm first
./advanced_tenant_onboard \
  --tenant-id offboard-test \
  --use-helm \
  --skip-dns \
  --skip-web-check

# Test offboarding with Helm cleanup
python3 advanced_tenant_offboard.py \
  --tenant-id offboard-test \
  --debug

# Verify Helm release is removed
helm list -n tenant-offboard-test
```

**Expected Results:**
- Helm release is detected and uninstalled
- All Helm-managed resources are cleaned up
- Namespace is properly removed

### Test 3.2: Mixed Deployment Cleanup
```bash
# Deploy tenant without Helm
./advanced_tenant_onboard \
  --tenant-id mixed-test \
  --skip-dns \
  --skip-web-check

# Test offboarding (should skip Helm cleanup)
python3 advanced_tenant_offboard.py \
  --tenant-id mixed-test \
  --debug
```

**Expected Results:**
- No Helm release found (expected)
- Individual resources are cleaned up normally
- No errors from missing Helm release

## Test Suite 4: End-to-End Integration Tests

### Test 4.1: Complete Helm Workflow
```bash
# Full onboarding with Helm
./advanced_tenant_onboard \
  --tenant-id e2e-helm-test \
  --use-helm \
  --production \
  --debug

# Verify deployment
kubectl get all -n tenant-e2e-helm-test
helm status tenant-e2e-helm-test -n tenant-e2e-helm-test

# Test application functionality
curl -k https://e2e-helm-test.architrave-assets.com/health

# Full offboarding
python3 advanced_tenant_offboard.py \
  --tenant-id e2e-helm-test \
  --delete-db \
  --debug

# Verify cleanup
kubectl get namespace tenant-e2e-helm-test
helm list -n tenant-e2e-helm-test
```

**Expected Results:**
- Complete deployment succeeds
- All components are functional
- Complete cleanup succeeds
- No resources remain

### Test 4.2: Resource Validation
```bash
# Deploy with Helm
./advanced_tenant_onboard \
  --tenant-id resource-test \
  --use-helm \
  --debug

# Validate all expected resources exist
kubectl get -n tenant-resource-test \
  deployment,service,configmap,secret,pvc,cronjob,virtualservice,destinationrule

# Check resource labels and annotations
kubectl get all -n tenant-resource-test -o yaml | grep -E "(labels|annotations):" -A 5
```

**Expected Results:**
- All expected resource types are present
- Resources have correct labels and annotations
- Tenant-specific configurations are applied

## Test Suite 5: Performance and Reliability Tests

### Test 5.1: Concurrent Deployments
```bash
# Test multiple concurrent Helm deployments
for i in {1..3}; do
  (
    ./advanced_tenant_onboard \
      --tenant-id concurrent-test-$i \
      --use-helm \
      --skip-dns \
      --skip-web-check \
      --debug
  ) &
done

wait

# Verify all deployments succeeded
for i in {1..3}; do
  helm status tenant-concurrent-test-$i -n tenant-concurrent-test-$i
done
```

**Expected Results:**
- All concurrent deployments succeed
- No resource conflicts
- Each tenant is properly isolated

### Test 5.2: Large Scale Test
```bash
# Deploy multiple tenants to test scalability
for i in {1..10}; do
  ./advanced_tenant_onboard \
    --tenant-id scale-test-$i \
    --use-helm \
    --minimal \
    --skip-dns \
    --skip-web-check
done

# Verify cluster health
kubectl top nodes
kubectl get pods --all-namespaces | grep -E "(scale-test|Error|CrashLoop)"

# Cleanup
for i in {1..10}; do
  python3 advanced_tenant_offboard.py --tenant-id scale-test-$i
done
```

**Expected Results:**
- All deployments succeed within reasonable time
- Cluster remains healthy
- Resource usage is within limits

## Test Suite 6: Error Handling and Edge Cases

### Test 6.1: Invalid Configuration
```bash
# Test with invalid tenant ID
./advanced_tenant_onboard \
  --tenant-id "invalid@tenant" \
  --use-helm \
  --debug

# Test with missing required values
helm install test-invalid ./helm-charts/architrave-tenant \
  --namespace test-invalid \
  --create-namespace
```

**Expected Results:**
- Invalid configurations are rejected
- Clear error messages are provided
- No partial deployments occur

### Test 6.2: Resource Conflicts
```bash
# Deploy tenant normally
./advanced_tenant_onboard \
  --tenant-id conflict-test \
  --skip-dns \
  --skip-web-check

# Try to deploy same tenant with Helm
./advanced_tenant_onboard \
  --tenant-id conflict-test \
  --use-helm \
  --debug
```

**Expected Results:**
- Conflicts are detected and handled gracefully
- Existing resources are not corrupted
- Clear error messages explain the conflict

## Automated Test Script

Create a comprehensive test runner:

```bash
#!/bin/bash
# File: run-helm-tests.sh

set -e

echo "🧪 Starting Helm Integration Test Suite"

# Test 1: Template validation
echo "📋 Running template validation tests..."
cd helm-charts/architrave-tenant
helm lint .
helm template test . --debug > /dev/null
echo "✅ Template validation passed"

# Test 2: Basic Helm deployment
echo "🚀 Testing basic Helm deployment..."
TENANT_ID="auto-test-$(date +%s)"
./../../tenant-management/scripts/advanced_tenant_onboard \
  --tenant-id $TENANT_ID \
  --use-helm \
  --skip-dns \
  --skip-web-check \
  --debug

# Verify deployment
helm status tenant-$TENANT_ID -n tenant-$TENANT_ID
kubectl get all -n tenant-$TENANT_ID

echo "✅ Basic Helm deployment passed"

# Test 3: Offboarding
echo "🧹 Testing Helm offboarding..."
python3 ../../tenant-management/scripts/advanced_tenant_offboard.py \
  --tenant-id $TENANT_ID \
  --debug

# Verify cleanup
if helm list -n tenant-$TENANT_ID | grep -q tenant-$TENANT_ID; then
  echo "❌ Helm release not cleaned up"
  exit 1
fi

echo "✅ Helm offboarding passed"

echo "🎉 All Helm integration tests passed!"
```

## Test Reporting

### Success Criteria
- [ ] All template validations pass
- [ ] Helm deployments complete successfully
- [ ] Fallback mechanism works correctly
- [ ] Offboarding cleans up Helm releases
- [ ] No resource leaks or conflicts
- [ ] Performance meets requirements

### Failure Investigation
When tests fail:
1. Check Helm release status: `helm status <release> -n <namespace>`
2. Review Kubernetes events: `kubectl get events -n <namespace>`
3. Check pod logs: `kubectl logs -n <namespace> <pod-name>`
4. Validate templates: `helm template <release> . --debug`
5. Review script logs with `--debug` flag

## Continuous Integration

Integrate these tests into CI/CD pipeline:
1. Run template validation on every commit
2. Run basic deployment tests on pull requests
3. Run full test suite on releases
4. Monitor test results and alert on failures
