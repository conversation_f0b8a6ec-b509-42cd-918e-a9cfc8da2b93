apiVersion: v1
items:
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-30T12:21:13+02:00"
    creationTimestamp: "2025-07-30T14:16:30Z"
    generateName: newtesttoday-backend-6b87556ddf-
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: 6b87556ddf
      tenant: newtesttoday
    name: newtesttoday-backend-6b87556ddf-fx75c
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newtesttoday-backend-6b87556ddf
      uid: c04bc338-77fe-434f-96b8-bef008bf4db1
    resourceVersion: "8180118"
    uid: e010a336-83f3-4a9e-aa0c-861733357d93
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: newtesttoday
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            key: host
            name: rds-secret
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            key: port
            name: rds-secret
      - name: DB_USERNAME
        valueFrom:
          secretKeyRef:
            key: username
            name: rds-secret
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            key: DB_NAME
            name: newtesttoday-db-secret
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: newtesttoday-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: password
            name: rds-secret
      - name: DB_DATABASE
        valueFrom:
          secretKeyRef:
            key: database
            name: rds-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: newtesttoday-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: newtesttoday-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: newtesttoday-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: "1"
          memory: 1Gi
        requests:
          cpu: 200m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant newtesttoday..."
        aws s3 sync s3://tenant-newtesttoday/newtesttoday/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-10-209.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: tenant-newtesttoday-s3-sa
    serviceAccountName: tenant-newtesttoday-s3-sa
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-54tbm
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:16:32Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:16:44Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:17:47Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:17:47Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:16:30Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://b5415add08e27fb1ab54d69aa74cdb910ae3e977fc53417759f196b40b71e3bd
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T14:16:44Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://2ee2f57c7e4dff7066b6b517cea8277d873c95ee50fc5e4dc4fc97afc8ec8ac2
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T14:16:44Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://486e43d90ebb8dbcc5ac36c38435a66781a7eaaa1144f4f46eb755c0ebf860e6
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:a5b482028826a2e109c53eb70c64ca70973c06265347103c7385f115aea772c7
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://486e43d90ebb8dbcc5ac36c38435a66781a7eaaa1144f4f46eb755c0ebf860e6
          exitCode: 0
          finishedAt: "2025-07-30T14:16:33Z"
          reason: Completed
          startedAt: "2025-07-30T14:16:32Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://c69ffe369b3f24ba199996046fdddb3cedd3ee39fbe1d95449b8b13ae3b0b1eb
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://c69ffe369b3f24ba199996046fdddb3cedd3ee39fbe1d95449b8b13ae3b0b1eb
          exitCode: 0
          finishedAt: "2025-07-30T14:16:35Z"
          reason: Completed
          startedAt: "2025-07-30T14:16:35Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://12b4852b10f37f6e0d186f83ac579e98313be75a1d826da12c4a265d27477df1
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://12b4852b10f37f6e0d186f83ac579e98313be75a1d826da12c4a265d27477df1
          exitCode: 0
          finishedAt: "2025-07-30T14:16:40Z"
          reason: Completed
          startedAt: "2025-07-30T14:16:36Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://de6bf268cdcb09026944a2fc64884a50c61e43f4b3b4e9683eb5785f03617f03
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://de6bf268cdcb09026944a2fc64884a50c61e43f4b3b4e9683eb5785f03617f03
          exitCode: 0
          finishedAt: "2025-07-30T14:16:41Z"
          reason: Completed
          startedAt: "2025-07-30T14:16:41Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://c5b2111495afa83c16407191e52eba81d0a5dc6fdcaf894a19242216f9b87378
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://c5b2111495afa83c16407191e52eba81d0a5dc6fdcaf894a19242216f9b87378
          exitCode: 0
          finishedAt: "2025-07-30T14:16:43Z"
          reason: Completed
          startedAt: "2025-07-30T14:16:42Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://5e6eec49c0f273ee73779321741500f97f0ce325abd5256df057b0d65854d4fa
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://5e6eec49c0f273ee73779321741500f97f0ce325abd5256df057b0d65854d4fa
          exitCode: 0
          finishedAt: "2025-07-30T14:16:44Z"
          reason: Completed
          startedAt: "2025-07-30T14:16:44Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-54tbm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-30T14:16:30Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-30T12:21:13+02:00"
    creationTimestamp: "2025-07-31T09:16:17Z"
    generateName: newtesttoday-backend-6b87556ddf-
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: 6b87556ddf
      tenant: newtesttoday
    name: newtesttoday-backend-6b87556ddf-p28lm
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newtesttoday-backend-6b87556ddf
      uid: c04bc338-77fe-434f-96b8-bef008bf4db1
    resourceVersion: "8602339"
    uid: 40e33316-900a-443d-9722-4aa77118737d
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: newtesttoday
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            key: host
            name: rds-secret
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            key: port
            name: rds-secret
      - name: DB_USERNAME
        valueFrom:
          secretKeyRef:
            key: username
            name: rds-secret
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            key: DB_NAME
            name: newtesttoday-db-secret
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: newtesttoday-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: password
            name: rds-secret
      - name: DB_DATABASE
        valueFrom:
          secretKeyRef:
            key: database
            name: rds-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: newtesttoday-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: newtesttoday-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: newtesttoday-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: "1"
          memory: 1Gi
        requests:
          cpu: 200m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant newtesttoday..."
        aws s3 sync s3://tenant-newtesttoday/newtesttoday/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-11-43.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: tenant-newtesttoday-s3-sa
    serviceAccountName: tenant-newtesttoday-s3-sa
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-5wgvm
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:16:36Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:17:49Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:18:55Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:18:55Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:16:17Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://4e5b909a520a864e8ff647e317ffa549fed45dc8972e322899f2d71638bc8bfa
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-31T09:17:49Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://c09bf2a4373786efc92131f00a7c735022ed7d037b839030eb358fe79637fe3f
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-31T09:17:53Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: **********
    hostIPs:
    - ip: **********
    initContainerStatuses:
    - containerID: containerd://2910fecc8d69a008718e41c97de1addbd31cdb03d554e8b81185d924ca1a2d2c
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:221149af55371db745f5fcc1fd553e5ab3d0ae1c6b5309a44c181e94a1913d65
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://2910fecc8d69a008718e41c97de1addbd31cdb03d554e8b81185d924ca1a2d2c
          exitCode: 0
          finishedAt: "2025-07-31T09:16:37Z"
          reason: Completed
          startedAt: "2025-07-31T09:16:35Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://f07dfefcca2ad895b9d949e2d7185edf97dff2cefc186844996645766e66edf2
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://f07dfefcca2ad895b9d949e2d7185edf97dff2cefc186844996645766e66edf2
          exitCode: 0
          finishedAt: "2025-07-31T09:16:41Z"
          reason: Completed
          startedAt: "2025-07-31T09:16:41Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://f718234295fc70f38a7d58afbe78f86dce3eead150fc747954df7dcf7487dfeb
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://f718234295fc70f38a7d58afbe78f86dce3eead150fc747954df7dcf7487dfeb
          exitCode: 0
          finishedAt: "2025-07-31T09:17:41Z"
          reason: Completed
          startedAt: "2025-07-31T09:17:34Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://a8076e488d3acf3eeb02bcb529498fe4f3e762b2a77b461eaeb197fc2b91849e
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://a8076e488d3acf3eeb02bcb529498fe4f3e762b2a77b461eaeb197fc2b91849e
          exitCode: 0
          finishedAt: "2025-07-31T09:17:45Z"
          reason: Completed
          startedAt: "2025-07-31T09:17:45Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://b1f23f5b692935ae465aa692d709d58540d93ac32a22ab91c2ed22ea378f7f88
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://b1f23f5b692935ae465aa692d709d58540d93ac32a22ab91c2ed22ea378f7f88
          exitCode: 0
          finishedAt: "2025-07-31T09:17:46Z"
          reason: Completed
          startedAt: "2025-07-31T09:17:45Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://7da314cbadcae67be54faba9d83d6b3d0160834c486342d2170acc4ab4b55344
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://7da314cbadcae67be54faba9d83d6b3d0160834c486342d2170acc4ab4b55344
          exitCode: 0
          finishedAt: "2025-07-31T09:17:49Z"
          reason: Completed
          startedAt: "2025-07-31T09:17:49Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-5wgvm
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: **********
    podIPs:
    - ip: **********
    qosClass: Burstable
    startTime: "2025-07-31T09:16:17Z"
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-30T17:39:39Z"
    generateName: newtesttoday-frontend-5f75c685d4-
    labels:
      app: newtesttoday-frontend
      component: frontend
      pod-template-hash: 5f75c685d4
      tenant: newtesttoday
    name: newtesttoday-frontend-5f75c685d4-tf9ds
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newtesttoday-frontend-5f75c685d4
      uid: eb6b305b-526c-444e-b604-b3725b5d93b3
    resourceVersion: "8255461"
    uid: dcbe7779-d2ff-449d-8d29-b9b1ce3e04da
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: newtesttoday
      - name: DOMAIN
        value: architrave-assets.de
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: BACKEND_URL
        value: http://newtesttoday-backend-service:8080
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        httpGet:
          path: /health
          port: 80
          scheme: HTTP
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 1
      name: frontend
      ports:
      - containerPort: 80
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        httpGet:
          path: /health
          port: 80
          scheme: HTTP
        initialDelaySeconds: 10
        periodSeconds: 10
        successThreshold: 1
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 128Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-trt7m
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for tenant newtesttoday..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 80;
            server_name newtesttoday.architrave-assets.de localhost;
            root /usr/share/nginx/html;
            index index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # API routes - proxy to backend service
            location /api/ {
                proxy_pass http://newtesttoday-backend-service:8080/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # Frontend routes - serve static files
            location / {
                try_files $uri $uri/ /index.html;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }

            # Static assets with caching
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1h;
                add_header Cache-Control "public, immutable";
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully for tenant newtesttoday"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newtesttoday-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-trt7m
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-12-210.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: tenant-newtesttoday-s3-sa
    serviceAccountName: tenant-newtesttoday-s3-sa
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-trt7m
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:39:42Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:39:42Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:40:00Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:40:00Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:39:39Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://7c1483ae280d08fc38a2b0b8c364e37deeb0d5e8c6691744c9f9ae310d099f96
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:42618102e5cc405c551c142acd857aaaa953559b63ca02fff46760e34d4ff149
      lastState: {}
      name: frontend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T17:39:48Z"
      volumeMounts:
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-trt7m
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://01213acc6215204fb47eb0c0340f8b3018316d2f4e229f059191950f57bf75d0
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://01213acc6215204fb47eb0c0340f8b3018316d2f4e229f059191950f57bf75d0
          exitCode: 0
          finishedAt: "2025-07-30T17:39:42Z"
          reason: Completed
          startedAt: "2025-07-30T17:39:42Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-trt7m
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-30T17:39:39Z"
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-30T17:24:31Z"
    generateName: newtesttoday-rabbitmq-5bc8b5f79b-
    labels:
      app: newtesttoday-rabbitmq
      component: rabbitmq
      pod-template-hash: 5bc8b5f79b
      tenant: newtesttoday
    name: newtesttoday-rabbitmq-5bc8b5f79b-tzzjx
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newtesttoday-rabbitmq-5bc8b5f79b
      uid: 22de1ba5-9d36-4b35-9fa9-bf3aae5956a7
    resourceVersion: "8249218"
    uid: 38b8b316-b1fc-40ae-9ddc-ef281843f965
  spec:
    containers:
    - env:
      - name: RABBITMQ_DEFAULT_USER
        value: guest
      - name: RABBITMQ_DEFAULT_PASS
        value: guest
      - name: TENANT_ID
        value: newtesttoday
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imagePullPolicy: IfNotPresent
      name: rabbitmq
      ports:
      - containerPort: 80
        protocol: TCP
      resources:
        limits:
          cpu: 500m
          memory: 1Gi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-lz4wh
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-10-209.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-lz4wh
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:37Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:31Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:37Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:37Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:31Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://334ad2c7874982c8484f7fa1b6fd25cff7d1026523ad485129c689a855dac396
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev@sha256:a40d91656430a34f8ee2dfbac222d974915ef9d448f7e99ecb4118218a8a84c0
      lastState: {}
      name: rabbitmq
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T17:24:37Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-lz4wh
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-30T17:24:31Z"
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newtesttoday-backend","component":"backend","tenant":"newtesttoday"},"name":"newtesttoday-backend-service","namespace":"tenant-newtesttoday"},"spec":{"ports":[{"name":"http","port":8080,"protocol":"TCP","targetPort":8080}],"selector":{"app":"newtesttoday-backend","component":"backend","tenant":"newtesttoday"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T10:05:41Z"
    labels:
      app: newtesttoday-backend
      component: backend
      tenant: newtesttoday
    name: newtesttoday-backend-service
    namespace: tenant-newtesttoday
    resourceVersion: "8084751"
    uid: a22a43d1-b0c3-432d-9888-bd201e60a886
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
    selector:
      app: newtesttoday-backend
      component: backend
      tenant: newtesttoday
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newtesttoday-frontend","component":"frontend","tenant":"newtesttoday"},"name":"newtesttoday-frontend-service","namespace":"tenant-newtesttoday"},"spec":{"ports":[{"port":80,"protocol":"TCP","targetPort":80}],"selector":{"app":"newtesttoday-frontend","component":"frontend","tenant":"newtesttoday"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T10:05:49Z"
    labels:
      app: newtesttoday-frontend
      component: frontend
      tenant: newtesttoday
    name: newtesttoday-frontend-service
    namespace: tenant-newtesttoday
    resourceVersion: "8084850"
    uid: cb77b1ce-af3c-4bab-985e-c64ac8537173
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 80
      protocol: TCP
      targetPort: 80
    selector:
      app: newtesttoday-frontend
      component: frontend
      tenant: newtesttoday
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newtesttoday-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"newtesttoday"},"name":"newtesttoday-rabbitmq-mgmt-service","namespace":"tenant-newtesttoday"},"spec":{"ports":[{"port":15672,"protocol":"TCP","targetPort":15672}],"selector":{"app":"newtesttoday-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"newtesttoday"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T10:02:46Z"
    labels:
      app: newtesttoday-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: newtesttoday
    name: newtesttoday-rabbitmq-mgmt-service
    namespace: tenant-newtesttoday
    resourceVersion: "8084868"
    uid: 260c898b-c9fe-45cf-93df-37391fa4d155
  spec:
    clusterIP: **************
    clusterIPs:
    - **************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 15672
      protocol: TCP
      targetPort: 15672
    selector:
      app: newtesttoday-rabbitmq
      component: rabbitmq
      tenant: newtesttoday
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newtesttoday-rabbitmq","component":"rabbitmq","tenant":"newtesttoday"},"name":"newtesttoday-rabbitmq-service","namespace":"tenant-newtesttoday"},"spec":{"ports":[{"port":5672,"protocol":"TCP","targetPort":5672}],"selector":{"app":"newtesttoday-rabbitmq","component":"rabbitmq","tenant":"newtesttoday"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T10:02:45Z"
    labels:
      app: newtesttoday-rabbitmq
      component: rabbitmq
      tenant: newtesttoday
    name: newtesttoday-rabbitmq-service
    namespace: tenant-newtesttoday
    resourceVersion: "8083522"
    uid: 61002c40-adcd-466a-9bf7-ef3b0d110fed
  spec:
    clusterIP: ************
    clusterIPs:
    - ************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 5672
      protocol: TCP
      targetPort: 5672
    selector:
      app: newtesttoday-rabbitmq
      component: rabbitmq
      tenant: newtesttoday
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "8"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"newtesttoday-backend","component":"backend","tenant":"newtesttoday"},"name":"newtesttoday-backend","namespace":"tenant-newtesttoday"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"newtesttoday-backend","component":"backend","tenant":"newtesttoday"}},"template":{"metadata":{"labels":{"app":"newtesttoday-backend","component":"backend","tenant":"newtesttoday"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"newtesttoday"},{"name":"DB_HOST","valueFrom":{"secretKeyRef":{"key":"DB_HOST","name":"newtesttoday-db-secret"}}},{"name":"DB_PORT","valueFrom":{"secretKeyRef":{"key":"DB_PORT","name":"newtesttoday-db-secret"}}},{"name":"DB_NAME","valueFrom":{"secretKeyRef":{"key":"DB_NAME","name":"newtesttoday-db-secret"}}},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"DB_USER","name":"newtesttoday-db-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"DB_PASSWORD","name":"newtesttoday-db-secret"}}},{"name":"DB_SSL","valueFrom":{"secretKeyRef":{"key":"DB_SSL","name":"newtesttoday-db-secret"}}},{"name":"DB_SSL_CA","valueFrom":{"secretKeyRef":{"key":"DB_SSL_CA","name":"newtesttoday-db-secret"}}},{"name":"DB_SSL_VERIFY","valueFrom":{"secretKeyRef":{"key":"DB_SSL_VERIFY","name":"newtesttoday-db-secret"}}},{"name":"RABBITMQ_URL","value":"amqp://guest:guest@newtesttoday-rabbitmq-service:5672/"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","livenessProbe":{"initialDelaySeconds":60,"periodSeconds":30,"tcpSocket":{"port":9000}},"name":"backend","ports":[{"containerPort":9000}],"readinessProbe":{"initialDelaySeconds":30,"periodSeconds":30,"tcpSocket":{"port":9000}},"resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}},"volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"},{"mountPath":"/shared-app","name":"shared-app"}]},{"image":"nginx:1.21-alpine","livenessProbe":{"failureThreshold":5,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-liveness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":90,"periodSeconds":60,"successThreshold":1,"timeoutSeconds":10},"name":"nginx","ports":[{"containerPort":8080}],"readinessProbe":{"failureThreshold":10,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-readiness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"timeoutSeconds":5},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"},{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Downloading RDS CA certificate...\"\ncurl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem\necho \"SSL certificate downloaded successfully\"\n"],"image":"curlimages/curl:latest","name":"ssl-cert-downloader","volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"}]},{"command":["sh","-c","echo \"Copying application files from /storage/ArchAssets to shared volume...\"\nif [ -d \"/storage/ArchAssets\" ]; then\n    cp -r /storage/ArchAssets/* /shared-app/ 2\u003e/dev/null || true\n    echo \"Application files copied successfully from /storage/ArchAssets\"\n    echo \"Copied files:\"\n    ls -la /shared-app/ | head -10\nelse\n    echo \"Warning: /storage/ArchAssets directory not found in webapp_dev image\"\n    echo \"Available directories in /storage:\"\n    ls -la /storage/ 2\u003e/dev/null || echo \"No /storage directory\"\n    echo \"Available directories in root:\"\n    ls -la / | grep -E \"(var|storage|app)\" || echo \"No relevant directories found\"\nfi\necho \"Application files copy process completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"app-files-copier","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Fixing PHP application config path issues and PHP-FPM compatibility...\"\n\n# Fix the relative path issue in /shared-app/public/api/index.php\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Found API index.php, fixing config path...\"\n\n  # Create backup\n  cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup\n\n  # Fix the relative path issue by using absolute path\n  sed -i \"s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g\" /shared-app/public/api/index.php\n\n  echo \"Config path fixed in API index.php\"\n  echo \"Checking the fix:\"\n  grep -n \"require.*config.*application.config.php\" /shared-app/public/api/index.php || echo \"Pattern not found after fix\"\nelse\n  echo \"Warning: /shared-app/public/api/index.php not found\"\n  echo \"Available files in /shared-app/public/:\"\n  ls -la /shared-app/public/ 2\u003e/dev/null || echo \"No public directory\"\nfi\n\n# Create PHP-FPM compatibility polyfill for apache_request_headers()\necho \"Creating PHP-FPM compatibility polyfill...\"\ncat \u003e /shared-app/php_fpm_polyfill.php \u003c\u003c 'POLYFILL_EOF'\n\u003c?php\n/**\n * PHP-FPM Compatibility Polyfill for Apache functions\n * This file provides missing Apache functions for PHP-FPM + nginx environments\n *\n * NOTE: Using forced function definition without conditional checks\n * because function_exists() may return false positives in some PHP-FPM environments\n */\n\n/**\n * Polyfill for apache_request_headers() function in PHP-FPM environment\n * Only define if the function doesn't already exist\n */\nif (!function_exists('apache_request_headers')) {\n    function apache_request_headers() {\n        $headers = array();\n\n        // Get all HTTP headers from $_SERVER superglobal\n        foreach ($_SERVER as $key =\u003e $value) {\n            if (strpos($key, 'HTTP_') === 0) {\n                // Convert HTTP_HEADER_NAME to Header-Name format\n                $header = str_replace('_', '-', substr($key, 5));\n                $header = ucwords(strtolower($header), '-');\n                $headers[$header] = $value;\n            }\n        }\n\n        // Add special headers that might not have HTTP_ prefix\n        if (isset($_SERVER['CONTENT_TYPE'])) {\n            $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];\n        }\n        if (isset($_SERVER['CONTENT_LENGTH'])) {\n            $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];\n        }\n\n        return $headers;\n    }\n}\n\n/**\n * Polyfill for apache_response_headers() function in PHP-FPM environment\n * @return array\n */\nfunction apache_response_headers() {\n    $headers = array();\n\n    // Get headers that were set with header() function\n    if (function_exists('headers_list')) {\n        foreach (headers_list() as $header) {\n            $parts = explode(':', $header, 2);\n            if (count($parts) === 2) {\n                $headers[trim($parts[0])] = trim($parts[1]);\n            }\n        }\n    }\n\n    return $headers;\n}\n\n/**\n * Alias for apache_request_headers() - commonly used alternative\n * Only define if the function doesn't already exist\n */\nif (!function_exists('getallheaders')) {\n    function getallheaders() {\n        return apache_request_headers();\n    }\n}\nPOLYFILL_EOF\n\necho \"✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php\"\n\n# Inject the polyfill into the main bootstrap file\nif [ -f \"/shared-app/bootstrap.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into bootstrap.php...\"\n\n  # Create backup\n  cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bootstrap.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into bootstrap.php\"\nelse\n  echo \"Warning: /shared-app/bootstrap.php not found\"\nfi\n\n# Also inject into the API index.php as a fallback\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into API index.php as fallback...\"\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/public/api/index.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into API index.php\"\nfi\n\n# CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)...\"\n\n  # Create backup\n  cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/architrave.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into CLI entry point\"\nelse\n  echo \"Warning: /shared-app/bin/architrave.php not found\"\nfi\n\n# Also check for alternative CLI entry points\nif [ -f \"/shared-app/bin/console.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into console.php...\"\n\n  # Create backup\n  cp /shared-app/bin/console.php /shared-app/bin/console.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/console.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into console.php\"\nfi\n\n# Verify config file exists\nif [ -f \"/shared-app/config/application.config.php\" ]; then\n  echo \"✅ Config file exists at /shared-app/config/application.config.php\"\nelse\n  echo \"❌ Config file missing at /shared-app/config/application.config.php\"\n  echo \"Available files in /shared-app/config/:\"\n  ls -la /shared-app/config/ 2\u003e/dev/null || echo \"No config directory\"\nfi\n\necho \"✅ PHP config path fix and PHP-FPM compatibility setup completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-config-fixer","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Validating PHP environment for PHP-FPM + nginx compatibility...\"\n\n# Test if polyfill was loaded correctly\nphp -r \"\nrequire_once '/shared-app/php_fpm_polyfill.php';\n\necho 'Testing PHP-FPM polyfill functions...\\n';\n\n// Test apache_request_headers function\nif (function_exists('apache_request_headers')) {\n    echo '✅ apache_request_headers() function is available\\n';\n} else {\n    echo '❌ apache_request_headers() function is missing\\n';\n    exit(1);\n}\n\n// Test getallheaders function\nif (function_exists('getallheaders')) {\n    echo '✅ getallheaders() function is available\\n';\n} else {\n    echo '❌ getallheaders() function is missing\\n';\n    exit(1);\n}\n\n// Test apache_response_headers function\nif (function_exists('apache_response_headers')) {\n    echo '✅ apache_response_headers() function is available\\n';\n} else {\n    echo '❌ apache_response_headers() function is missing\\n';\n    exit(1);\n}\n\necho '✅ All PHP-FPM polyfill functions are working correctly\\n';\n\"\n\n# Test basic PHP application loading\necho \"Testing PHP application bootstrap loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    echo '✅ Bootstrap loaded successfully\\n';\n} catch (Exception \\$e) {\n    echo '❌ Bootstrap loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# Test Laminas application config loading\necho \"Testing Laminas application config loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    \\$config = require '/shared-app/config/application.config.php';\n    echo '✅ Application config loaded successfully\\n';\n    echo 'Config modules: ' . count(\\$config['modules']) . '\\n';\n} catch (Exception \\$e) {\n    echo '❌ Application config loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# CRITICAL FIX: Test CLI entry point functionality\necho \"Testing CLI entry point functionality...\"\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Testing bin/architrave.php polyfill integration...\"\n  php -d display_errors=1 -d error_reporting=E_ALL -r \"\n  try {\n      // Change to the correct directory\n      chdir('/shared-app');\n\n      // Test if polyfill file exists and is readable\n      if (!file_exists('php_fpm_polyfill.php')) {\n          echo '❌ Polyfill file not found\\n';\n          exit(1);\n      }\n\n      // Load polyfill manually to test\n      require_once 'php_fpm_polyfill.php';\n\n      // Test if functions are available\n      if (function_exists('apache_request_headers')) {\n          echo '✅ CLI polyfill functions available\\n';\n      } else {\n          echo '❌ CLI polyfill functions missing\\n';\n          exit(1);\n      }\n\n      echo '✅ CLI environment validation successful\\n';\n  } catch (Exception \\$e) {\n      echo '⚠️ CLI test failed: ' . \\$e-\u003egetMessage() . '\\n';\n      echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n      echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n      // Don't exit with error as this might fail in init container context\n  }\n  \" || echo \"⚠️ CLI test completed with warnings (normal in init container)\"\nelse\n  echo \"⚠️ CLI entry point not found - will be available after application sync\"\nfi\n\necho \"✅ PHP environment validation completed successfully\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-env-validator","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Creating HTTP nginx configuration for port 8080...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 8080;\n    server_name localhost;\n    root /shared-app/public;\n    index index.php index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /api/health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # Root endpoint - redirect to API\n    location = / {\n        return 302 /api/;\n    }\n\n    location / {\n        try_files $uri $uri/ /api/index.php?$args;\n    }\n\n    location ~ \\.php$ {\n        fastcgi_pass localhost:9000;\n        fastcgi_index index.php;\n        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\n        fastcgi_param AUTOMATED_TEST 1;\n        include fastcgi_params;\n        fastcgi_read_timeout 310;\n    }\n\n    location ~ /\\.ht {\n        deny all;\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"ssl-certs"},{"emptyDir":{},"name":"shared-app"},{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-30T10:02:47Z"
    generation: 9
    labels:
      app: newtesttoday-backend
      component: backend
      tenant: newtesttoday
    name: newtesttoday-backend
    namespace: tenant-newtesttoday
    resourceVersion: "8602349"
    uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
  spec:
    progressDeadlineSeconds: 600
    replicas: 2
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        tenant: newtesttoday
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:21:13+02:00"
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: host
                name: rds-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: port
                name: rds-secret
          - name: DB_USERNAME
            valueFrom:
              secretKeyRef:
                key: username
                name: rds-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: password
                name: rds-secret
          - name: DB_DATABASE
            valueFrom:
              secretKeyRef:
                key: database
                name: rds-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant newtesttoday..."
            aws s3 sync s3://tenant-newtesttoday/newtesttoday/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: tenant-newtesttoday-s3-sa
        serviceAccountName: tenant-newtesttoday-s3-sa
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    conditions:
    - lastTransitionTime: "2025-07-30T10:02:47Z"
      lastUpdateTime: "2025-07-30T10:24:14Z"
      message: ReplicaSet "newtesttoday-backend-6b87556ddf" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-31T09:18:55Z"
      lastUpdateTime: "2025-07-31T09:18:55Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 9
    readyReplicas: 2
    replicas: 2
    updatedReplicas: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "3"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"newtesttoday-frontend","component":"frontend","tenant":"newtesttoday"},"name":"newtesttoday-frontend","namespace":"tenant-newtesttoday"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"newtesttoday-frontend","component":"frontend","tenant":"newtesttoday"}},"template":{"metadata":{"labels":{"app":"newtesttoday-frontend","component":"frontend","tenant":"newtesttoday"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"newtesttoday"},{"name":"DOMAIN","value":"architrave-assets.de"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"},{"name":"BACKEND_URL","value":"http://newtesttoday-backend-service:8080"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl","livenessProbe":{"httpGet":{"path":"/health","port":80},"initialDelaySeconds":30,"periodSeconds":30},"name":"frontend","ports":[{"containerPort":80}],"readinessProbe":{"httpGet":{"path":"/health","port":80},"initialDelaySeconds":10,"periodSeconds":10},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Creating HTTP nginx configuration for tenant newtesttoday...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 80;\n    server_name newtesttoday.architrave-assets.de localhost;\n    root /usr/share/nginx/html;\n    index index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # API routes - proxy to backend service\n    location /api/ {\n        proxy_pass http://newtesttoday-backend-service:8080/api/;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n\n    # Frontend routes - serve static files\n    location / {\n        try_files $uri $uri/ /index.html;\n        add_header Cache-Control \"no-cache, no-store, must-revalidate\";\n        add_header Pragma \"no-cache\";\n        add_header Expires \"0\";\n    }\n\n    # Static assets with caching\n    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully for tenant newtesttoday\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-30T10:05:48Z"
    generation: 3
    labels:
      app: newtesttoday-frontend
      component: frontend
      tenant: newtesttoday
    name: newtesttoday-frontend
    namespace: tenant-newtesttoday
    resourceVersion: "8255467"
    uid: fa1360d3-4828-424b-906b-eae97abab948
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: newtesttoday-frontend
        component: frontend
        tenant: newtesttoday
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newtesttoday-frontend
          component: frontend
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newtesttoday
          - name: DOMAIN
            value: architrave-assets.de
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://newtesttoday-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 128Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant newtesttoday..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name newtesttoday.architrave-assets.de localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://newtesttoday-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant newtesttoday"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: tenant-newtesttoday-s3-sa
        serviceAccountName: tenant-newtesttoday-s3-sa
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-30T10:05:48Z"
      lastUpdateTime: "2025-07-30T10:16:12Z"
      message: ReplicaSet "newtesttoday-frontend-5f75c685d4" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-30T17:40:01Z"
      lastUpdateTime: "2025-07-30T17:40:01Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 3
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "2"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"newtesttoday-rabbitmq","component":"rabbitmq","tenant":"newtesttoday"},"name":"newtesttoday-rabbitmq","namespace":"tenant-newtesttoday"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"newtesttoday-rabbitmq","component":"rabbitmq","tenant":"newtesttoday"}},"template":{"metadata":{"labels":{"app":"newtesttoday-rabbitmq","component":"rabbitmq","tenant":"newtesttoday"}},"spec":{"containers":[{"env":[{"name":"RABBITMQ_DEFAULT_USER","value":"guest"},{"name":"RABBITMQ_DEFAULT_PASS","value":"guest"},{"name":"TENANT_ID","value":"newtesttoday"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02","name":"rabbitmq","ports":[{"containerPort":80}]}]}}}}
    creationTimestamp: "2025-07-30T10:02:44Z"
    generation: 2
    labels:
      app: newtesttoday-rabbitmq
      component: rabbitmq
      tenant: newtesttoday
    name: newtesttoday-rabbitmq
    namespace: tenant-newtesttoday
    resourceVersion: "8249225"
    uid: 65c30888-0758-4d60-8329-7464c228dd73
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: newtesttoday-rabbitmq
        component: rabbitmq
        tenant: newtesttoday
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newtesttoday-rabbitmq
          component: rabbitmq
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: newtesttoday
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-30T10:02:44Z"
      lastUpdateTime: "2025-07-30T10:14:59Z"
      message: ReplicaSet "newtesttoday-rabbitmq-5bc8b5f79b" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-30T17:24:37Z"
      lastUpdateTime: "2025-07-30T17:24:37Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 2
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "6"
    creationTimestamp: "2025-07-30T10:15:58Z"
    generation: 5
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: "5849756899"
      tenant: newtesttoday
    name: newtesttoday-backend-5849756899
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-backend
      uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
    resourceVersion: "8091161"
    uid: d4aee820-7e29-4a72-b13b-b8255cd2dd3f
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        pod-template-hash: "5849756899"
        tenant: newtesttoday
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:05:46+02:00"
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          pod-template-hash: "5849756899"
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newtesttoday-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newtesttoday-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newtesttoday-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant newtesttoday..."
            aws s3 sync s3://tenant-newtesttoday/newtesttoday/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: tenant-newtesttoday-s3-sa
        serviceAccountName: tenant-newtesttoday-s3-sa
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 5
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-30T10:04:30Z"
    generation: 2
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: 67476c98c8
      tenant: newtesttoday
    name: newtesttoday-backend-67476c98c8
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-backend
      uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
    resourceVersion: "8084809"
    uid: 0e33b1ce-d8fb-4281-922d-53cac42e758b
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        pod-template-hash: 67476c98c8
        tenant: newtesttoday
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:04:30+02:00"
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          pod-template-hash: 67476c98c8
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newtesttoday-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newtesttoday-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newtesttoday-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "8"
    creationTimestamp: "2025-07-30T10:21:13Z"
    generation: 2
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: 6b87556ddf
      tenant: newtesttoday
    name: newtesttoday-backend-6b87556ddf
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-backend
      uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
    resourceVersion: "8602342"
    uid: c04bc338-77fe-434f-96b8-bef008bf4db1
  spec:
    replicas: 2
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        pod-template-hash: 6b87556ddf
        tenant: newtesttoday
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:21:13+02:00"
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          pod-template-hash: 6b87556ddf
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: host
                name: rds-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: port
                name: rds-secret
          - name: DB_USERNAME
            valueFrom:
              secretKeyRef:
                key: username
                name: rds-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: password
                name: rds-secret
          - name: DB_DATABASE
            valueFrom:
              secretKeyRef:
                key: database
                name: rds-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant newtesttoday..."
            aws s3 sync s3://tenant-newtesttoday/newtesttoday/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: tenant-newtesttoday-s3-sa
        serviceAccountName: tenant-newtesttoday-s3-sa
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    fullyLabeledReplicas: 2
    observedGeneration: 2
    readyReplicas: 2
    replicas: 2
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "5"
    creationTimestamp: "2025-07-30T10:14:57Z"
    generation: 2
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: 6cd5b4ddff
      tenant: newtesttoday
    name: newtesttoday-backend-6cd5b4ddff
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-backend
      uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
    resourceVersion: "8089181"
    uid: 9a3face1-96ce-4110-9a65-b766239be524
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        pod-template-hash: 6cd5b4ddff
        tenant: newtesttoday
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:05:46+02:00"
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          pod-template-hash: 6cd5b4ddff
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newtesttoday-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newtesttoday-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newtesttoday-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant newtesttoday..."
            aws s3 sync s3://tenant-newtesttoday/newtesttoday/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: newtesttoday-s3-service-account
        serviceAccountName: newtesttoday-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "3"
    creationTimestamp: "2025-07-30T10:05:47Z"
    generation: 3
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: 757bf659b6
      tenant: newtesttoday
    name: newtesttoday-backend-757bf659b6
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-backend
      uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
    resourceVersion: "8084945"
    uid: ac8fa503-ecb3-4f6e-9854-5951593a07c7
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        pod-template-hash: 757bf659b6
        tenant: newtesttoday
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:05:46+02:00"
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          pod-template-hash: 757bf659b6
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newtesttoday-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newtesttoday-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newtesttoday-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 3
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "4"
    creationTimestamp: "2025-07-30T10:05:58Z"
    generation: 5
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: 75ff9c4995
      tenant: newtesttoday
    name: newtesttoday-backend-75ff9c4995
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-backend
      uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
    resourceVersion: "8090630"
    uid: db52ed1b-8ee2-4018-b5e4-84bd17662926
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        pod-template-hash: 75ff9c4995
        tenant: newtesttoday
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:05:46+02:00"
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          pod-template-hash: 75ff9c4995
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newtesttoday-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newtesttoday-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newtesttoday-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant newtesttoday..."
            aws s3 sync s3://tenant-newtesttoday/newtesttoday/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: newtesttoday-s3-service-account
        serviceAccountName: newtesttoday-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 5
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-30T10:02:47Z"
    generation: 2
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: 87d5db795
      tenant: newtesttoday
    name: newtesttoday-backend-87d5db795
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-backend
      uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
    resourceVersion: "8085603"
    uid: 034f7990-4806-443a-b3fa-9bd0763dc2aa
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        pod-template-hash: 87d5db795
        tenant: newtesttoday
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          pod-template-hash: 87d5db795
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newtesttoday-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newtesttoday-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newtesttoday-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "7"
    creationTimestamp: "2025-07-30T10:17:49Z"
    generation: 5
    labels:
      app: newtesttoday-backend
      component: backend
      pod-template-hash: fd577f94f
      tenant: newtesttoday
    name: newtesttoday-backend-fd577f94f
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-backend
      uid: 6059ad25-48f6-44dc-9fac-12a8cacb5969
    resourceVersion: "8092706"
    uid: baf76831-34cb-4c3c-8a57-7a869aa103a7
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-backend
        component: backend
        pod-template-hash: fd577f94f
        tenant: newtesttoday
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:05:46+02:00"
        creationTimestamp: null
        labels:
          app: newtesttoday-backend
          component: backend
          pod-template-hash: fd577f94f
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: newtesttoday
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: host
                name: rds-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: port
                name: rds-secret
          - name: DB_USERNAME
            valueFrom:
              secretKeyRef:
                key: username
                name: rds-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newtesttoday-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newtesttoday-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: password
                name: rds-secret
          - name: DB_DATABASE
            valueFrom:
              secretKeyRef:
                key: database
                name: rds-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newtesttoday-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newtesttoday-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newtesttoday-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newtesttoday-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant newtesttoday..."
            aws s3 sync s3://tenant-newtesttoday/newtesttoday/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: tenant-newtesttoday-s3-sa
        serviceAccountName: tenant-newtesttoday-s3-sa
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 5
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-30T10:05:48Z"
    generation: 2
    labels:
      app: newtesttoday-frontend
      component: frontend
      pod-template-hash: 5454f6469
      tenant: newtesttoday
    name: newtesttoday-frontend-5454f6469
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-frontend
      uid: fa1360d3-4828-424b-906b-eae97abab948
    resourceVersion: "8088839"
    uid: 87c8cddc-8178-4172-ae39-a047e11a1a6b
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-frontend
        component: frontend
        pod-template-hash: 5454f6469
        tenant: newtesttoday
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newtesttoday-frontend
          component: frontend
          pod-template-hash: 5454f6469
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newtesttoday
          - name: DOMAIN
            value: architrave-assets.de
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://newtesttoday-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant newtesttoday..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name newtesttoday.architrave-assets.de localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://newtesttoday-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant newtesttoday"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "3"
    creationTimestamp: "2025-07-30T10:15:57Z"
    generation: 1
    labels:
      app: newtesttoday-frontend
      component: frontend
      pod-template-hash: 5f75c685d4
      tenant: newtesttoday
    name: newtesttoday-frontend-5f75c685d4
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-frontend
      uid: fa1360d3-4828-424b-906b-eae97abab948
    resourceVersion: "8255466"
    uid: eb6b305b-526c-444e-b604-b3725b5d93b3
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: newtesttoday-frontend
        component: frontend
        pod-template-hash: 5f75c685d4
        tenant: newtesttoday
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newtesttoday-frontend
          component: frontend
          pod-template-hash: 5f75c685d4
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newtesttoday
          - name: DOMAIN
            value: architrave-assets.de
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://newtesttoday-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 128Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant newtesttoday..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name newtesttoday.architrave-assets.de localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://newtesttoday-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant newtesttoday"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: tenant-newtesttoday-s3-sa
        serviceAccountName: tenant-newtesttoday-s3-sa
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-30T10:14:59Z"
    generation: 2
    labels:
      app: newtesttoday-frontend
      component: frontend
      pod-template-hash: 5fc458647d
      tenant: newtesttoday
    name: newtesttoday-frontend-5fc458647d
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-frontend
      uid: fa1360d3-4828-424b-906b-eae97abab948
    resourceVersion: "8089347"
    uid: ccad7d6f-fce4-40cd-8169-413263ebcc24
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-frontend
        component: frontend
        pod-template-hash: 5fc458647d
        tenant: newtesttoday
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newtesttoday-frontend
          component: frontend
          pod-template-hash: 5fc458647d
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newtesttoday
          - name: DOMAIN
            value: architrave-assets.de
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://newtesttoday-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 128Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant newtesttoday..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name newtesttoday.architrave-assets.de localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://newtesttoday-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant newtesttoday"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-30T10:14:58Z"
    generation: 1
    labels:
      app: newtesttoday-rabbitmq
      component: rabbitmq
      pod-template-hash: 5bc8b5f79b
      tenant: newtesttoday
    name: newtesttoday-rabbitmq-5bc8b5f79b
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-rabbitmq
      uid: 65c30888-0758-4d60-8329-7464c228dd73
    resourceVersion: "8249222"
    uid: 22de1ba5-9d36-4b35-9fa9-bf3aae5956a7
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: newtesttoday-rabbitmq
        component: rabbitmq
        pod-template-hash: 5bc8b5f79b
        tenant: newtesttoday
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newtesttoday-rabbitmq
          component: rabbitmq
          pod-template-hash: 5bc8b5f79b
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: newtesttoday
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-30T10:02:44Z"
    generation: 2
    labels:
      app: newtesttoday-rabbitmq
      component: rabbitmq
      pod-template-hash: 747d5f4f8f
      tenant: newtesttoday
    name: newtesttoday-rabbitmq-747d5f4f8f
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newtesttoday-rabbitmq
      uid: 65c30888-0758-4d60-8329-7464c228dd73
    resourceVersion: "8088672"
    uid: d1063a46-0c1b-4599-900c-fad3f4cbc2ee
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newtesttoday-rabbitmq
        component: rabbitmq
        pod-template-hash: 747d5f4f8f
        tenant: newtesttoday
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newtesttoday-rabbitmq
          component: rabbitmq
          pod-template-hash: 747d5f4f8f
          tenant: newtesttoday
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: newtesttoday
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"keda.sh/v1alpha1","kind":"ScaledObject","metadata":{"annotations":{},"labels":{"app":"newtesttoday-backend","tenant":"newtesttoday"},"name":"newtesttoday-backend-scaler","namespace":"tenant-newtesttoday"},"spec":{"advanced":{"horizontalPodAutoscalerConfig":{"behavior":{"scaleDown":{"policies":[{"periodSeconds":60,"type":"Percent","value":50}],"stabilizationWindowSeconds":300},"scaleUp":{"policies":[{"periodSeconds":30,"type":"Percent","value":100},{"periodSeconds":30,"type":"Pods","value":2}],"selectPolicy":"Max","stabilizationWindowSeconds":0}}}},"cooldownPeriod":300,"maxReplicaCount":20,"minReplicaCount":2,"pollingInterval":15,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"newtesttoday-backend"},"triggers":[{"metadata":{"value":"70"},"metricType":"Utilization","type":"cpu"},{"metadata":{"value":"80"},"metricType":"Utilization","type":"memory"},{"metadata":{"metricName":"http_requests_per_second","query":"sum(rate(istio_requests_total{destination_service_name=\"newtesttoday-backend\"}[2m]))","serverAddress":"http://prometheus-server.monitoring.svc.cluster.local:9090","threshold":"50"},"type":"prometheus"},{"metadata":{"host":"newtesttoday-rabbitmq.tenant-newtesttoday.svc.cluster.local","mode":"QueueLength","password":"guest","port":"15672","protocol":"http","queueName":"tenant-queue","username":"guest","value":"5"},"type":"rabbitmq"}]}}
    creationTimestamp: "2025-07-30T10:08:15Z"
    labels:
      app: newtesttoday-backend
      app.kubernetes.io/managed-by: keda-operator
      app.kubernetes.io/name: keda-hpa-newtesttoday-backend-scaler
      app.kubernetes.io/part-of: newtesttoday-backend-scaler
      app.kubernetes.io/version: 2.17.2
      scaledobject.keda.sh/name: newtesttoday-backend-scaler
      tenant: newtesttoday
    name: keda-hpa-newtesttoday-backend-scaler
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: keda.sh/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: ScaledObject
      name: newtesttoday-backend-scaler
      uid: 7742dc60-01ed-4055-8471-065246c84835
    resourceVersion: "8602438"
    uid: c4dbfa39-6886-42b9-b8ab-5990709bcccc
  spec:
    behavior:
      scaleDown:
        policies:
        - periodSeconds: 60
          type: Percent
          value: 50
        selectPolicy: Max
        stabilizationWindowSeconds: 300
      scaleUp:
        policies:
        - periodSeconds: 30
          type: Percent
          value: 100
        - periodSeconds: 30
          type: Pods
          value: 2
        selectPolicy: Max
        stabilizationWindowSeconds: 0
    maxReplicas: 20
    metrics:
    - external:
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newtesttoday-backend-scaler
        target:
          averageValue: "50"
          type: AverageValue
      type: External
    - external:
        metric:
          name: s3-rabbitmq-tenant-queue
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newtesttoday-backend-scaler
        target:
          averageValue: "5"
          type: AverageValue
      type: External
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 2
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: newtesttoday-backend
  status:
    conditions:
    - lastTransitionTime: "2025-07-30T10:08:30Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-31T09:18:25Z"
      message: 'the HPA was unable to compute the replica count: unable to get external
        metric tenant-newtesttoday/s3-rabbitmq-tenant-queue/&LabelSelector{MatchLabels:map[string]string{scaledobject.keda.sh/name:
        newtesttoday-backend-scaler,},MatchExpressions:[]LabelSelectorRequirement{},}:
        unable to fetch metrics from external metrics API: rpc error: code = Unknown
        desc = error when getting metric values metric:s3-rabbitmq-tenant-queue encountered
        error'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-07-30T10:09:00Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
    currentMetrics:
    - external:
        current:
          averageValue: "0"
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newtesttoday-backend-scaler
      type: External
    - type: ""
    - resource:
        current:
          averageUtilization: 0
          averageValue: 2m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 4
          averageValue: "16154624"
        name: memory
      type: Resource
    currentReplicas: 2
    desiredReplicas: 2
    lastScaleTime: "2025-07-30T10:08:30Z"
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"keda.sh/v1alpha1","kind":"ScaledObject","metadata":{"annotations":{},"labels":{"app":"newtesttoday-frontend","tenant":"newtesttoday"},"name":"newtesttoday-frontend-scaler","namespace":"tenant-newtesttoday"},"spec":{"cooldownPeriod":300,"maxReplicaCount":10,"minReplicaCount":1,"pollingInterval":15,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"newtesttoday-frontend"},"triggers":[{"metadata":{"value":"70"},"metricType":"Utilization","type":"cpu"},{"metadata":{"value":"80"},"metricType":"Utilization","type":"memory"},{"metadata":{"metricName":"http_requests_per_second","query":"sum(rate(istio_requests_total{destination_service_name=\"newtesttoday-frontend\"}[2m]))","serverAddress":"http://prometheus-server.monitoring.svc.cluster.local:9090","threshold":"30"},"type":"prometheus"}]}}
    creationTimestamp: "2025-07-30T10:08:17Z"
    labels:
      app: newtesttoday-frontend
      app.kubernetes.io/managed-by: keda-operator
      app.kubernetes.io/name: keda-hpa-newtesttoday-frontend-scaler
      app.kubernetes.io/part-of: newtesttoday-frontend-scaler
      app.kubernetes.io/version: 2.17.2
      scaledobject.keda.sh/name: newtesttoday-frontend-scaler
      tenant: newtesttoday
    name: keda-hpa-newtesttoday-frontend-scaler
    namespace: tenant-newtesttoday
    ownerReferences:
    - apiVersion: keda.sh/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: ScaledObject
      name: newtesttoday-frontend-scaler
      uid: 4081e470-b70a-4479-97b7-50762d492061
    resourceVersion: "8602333"
    uid: 1c6d6cf2-210a-4454-a79c-aa86c9933597
  spec:
    maxReplicas: 10
    metrics:
    - external:
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newtesttoday-frontend-scaler
        target:
          averageValue: "30"
          type: AverageValue
      type: External
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 1
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: newtesttoday-frontend
  status:
    conditions:
    - lastTransitionTime: "2025-07-30T10:08:32Z"
      message: recommended size matches current size
      reason: ReadyForNewScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-30T17:40:20Z"
      message: the HPA was able to successfully calculate a replica count from cpu
        resource utilization (percentage of request)
      reason: ValidMetricFound
      status: "True"
      type: ScalingActive
    - lastTransitionTime: "2025-07-30T10:08:32Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
    currentMetrics:
    - external:
        current:
          averageValue: "0"
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newtesttoday-frontend-scaler
      type: External
    - resource:
        current:
          averageUtilization: 1
          averageValue: 1m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 2
          averageValue: "3215360"
        name: memory
      type: Resource
    currentReplicas: 1
    desiredReplicas: 1
kind: List
metadata:
  resourceVersion: ""
