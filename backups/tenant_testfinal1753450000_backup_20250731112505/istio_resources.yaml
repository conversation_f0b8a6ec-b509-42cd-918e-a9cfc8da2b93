apiVersion: v1
items:
- apiVersion: networking.istio.io/v1
  kind: VirtualService
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.istio.io/v1beta1","kind":"VirtualService","metadata":{"annotations":{},"labels":{"managed-by":"tenant-onboarding","tenant":"testfinal1753450000"},"name":"tenant-testfinal1753450000-vs","namespace":"tenant-testfinal1753450000"},"spec":{"gateways":["istio-system/tenant-gateway"],"hosts":["testfinal1753450000.architrave-assets.de"],"http":[{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"match":[{"uri":{"prefix":"/api"}}],"retries":{"attempts":3,"perTryTimeout":"10s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"testfinal1753450000-backend-service","port":{"number":8080}}}],"timeout":"30s"},{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"retries":{"attempts":2,"perTryTimeout":"5s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"testfinal1753450000-frontend-service","port":{"number":80}}}],"timeout":"15s"}]}}
    creationTimestamp: "2025-07-25T13:53:28Z"
    generation: 1
    labels:
      managed-by: tenant-onboarding
      tenant: testfinal1753450000
    name: tenant-testfinal1753450000-vs
    namespace: tenant-testfinal1753450000
    resourceVersion: "5568596"
    uid: cb34e97f-d78e-43a5-86a6-31918dc63c47
  spec:
    gateways:
    - istio-system/tenant-gateway
    hosts:
    - testfinal1753450000.architrave-assets.de
    http:
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      match:
      - uri:
          prefix: /api
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: testfinal1753450000-backend-service
          port:
            number: 8080
      timeout: 30s
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      retries:
        attempts: 2
        perTryTimeout: 5s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: testfinal1753450000-frontend-service
          port:
            number: 80
      timeout: 15s
kind: List
metadata:
  resourceVersion: ""
