apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: dGVuYW50X25ld3Rlc3R0b2RheQ==
    DB_PASSWORD: UDdPSnZRTDloKzNWVFBrZyU=
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_SSL_VERIFY: ZmFsc2U=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"dGVuYW50X25ld3Rlc3R0b2RheQ==","DB_PASSWORD":"UDdPSnZRTDloKzNWVFBrZyU=","DB_PORT":"MzMwNg==","DB_SSL":"dHJ1ZQ==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_SSL_VERIFY":"ZmFsc2U=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"newtesttoday-db-secret","namespace":"tenant-newtesttoday"}}
    creationTimestamp: "2025-07-30T10:05:45Z"
    name: newtesttoday-db-secret
    namespace: tenant-newtesttoday
    resourceVersion: "8084773"
    uid: 04902d53-8474-4a7c-aa5f-7b156f6afbad
  type: Opaque
- apiVersion: v1
  data:
    database: YXJjaGl0cmF2ZQ==
    host: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    password: eyJ1c2VybmFtZSI6ImFkbWluIiwicGFzc3dvcmQiOiJQN09KdlFMOWgrM1ZUUGtnJSIsImVuZ2luZSI6Im15c3FsIiwiaG9zdCI6InByb2R1Y3Rpb24tYXVyb3JhLXNlcnZlcmxlc3MuY2x1c3Rlci1jcG1hZ3draTJrdjguZXUtY2VudHJhbC0xLnJkcy5hbWF6b25hd3MuY29tIiwicG9ydCI6MzMwNiwiZGJuYW1lIjoiYXJjaGl0cmF2ZSIsImRiSW5zdGFuY2VJZGVudGlmaWVyIjoicHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcyJ9
    port: MzMwNg==
    username: YWRtaW4=
  kind: Secret
  metadata:
    creationTimestamp: "2025-07-30T10:17:47Z"
    name: rds-secret
    namespace: tenant-newtesttoday
    resourceVersion: "8090036"
    uid: 9634f69a-d3ea-4dae-ab37-fb6d65953090
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
