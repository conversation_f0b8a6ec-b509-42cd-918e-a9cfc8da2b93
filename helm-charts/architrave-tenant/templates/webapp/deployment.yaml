{{- if .Values.webapp.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenant.id }}-webapp
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: webapp
    component: webapp
  annotations:
    owner: architrave
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.webapp.replicas }}
  selector:
    matchLabels:
      {{- include "architrave-tenant.selectorLabels" . | nindent 6 }}
      app: webapp
      component: webapp
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        {{- include "architrave-tenant.selectorLabels" . | nindent 8 }}
        app: webapp
        component: webapp
      annotations:
        owner: architrave
        {{- with .Values.annotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      securityContext:
        fsGroup: 33
      initContainers:
        - name: nginx-config-setup
          image: busybox:latest
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Setting up nginx configuration for HTTP on port 8080..."
              cat > /etc/nginx/conf.d/default.conf << 'EOF'
              server {
                  listen 8080;
                  server_name localhost;
                  root /storage/ArchAssets/public;
                  index index.php index.html;

                  location / {
                      try_files $uri $uri/ /index.php?$query_string;
                  }

                  location ~ \.php$ {
                      fastcgi_pass 127.0.0.1:9000;
                      fastcgi_index index.php;
                      fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                      include fastcgi_params;
                      fastcgi_read_timeout 300;
                      fastcgi_buffer_size 128k;
                      fastcgi_buffers 4 256k;
                      fastcgi_busy_buffers_size 256k;
                  }

                  location ~ /\.ht {
                      deny all;
                  }

                  location /api/ {
                      try_files $uri $uri/ /api/index.php?$query_string;
                  }

                  location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                      expires 1y;
                      add_header Cache-Control "public, immutable";
                  }
              }
              EOF
              echo "Nginx configuration created successfully"
          volumeMounts:
            - mountPath: /etc/nginx/conf.d
              name: nginx-config-volume

        - name: ssl-cert-downloader
          image: curlimages/curl:latest
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Downloading RDS SSL certificate..."
              curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
              chmod 644 /tmp/rds-ca-2019-root.pem
              echo "RDS SSL certificate downloaded successfully"
              ls -la /tmp/
          env:
            - name: AWS_DEFAULT_REGION
              value: "eu-central-1"
          volumeMounts:
            - mountPath: /tmp
              name: ssl-cert-volume
        - name: local-php-config-setup
          image: {{ .Values.images.webapp }}
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Setting up local.php configuration..."
              mkdir -p /storage/ArchAssets/config/autoload
              cat > /storage/ArchAssets/config/autoload/local.php << 'EOF'
              <?php

              $config = [
                  'doctrine' => [
                      'connection' => [
                          'orm_default' => [
                              'driverClass' => 'Doctrine\DBAL\Driver\PDOMySql\Driver',
                              'params' => [
                                  'host' => getenv('MYSQL_HOST'),
                                  'port' => '3306',
                                  'user' => getenv('MYSQL_USER'),
                                  'dbname' => getenv('MYSQL_DATABASE'),
                                  'password' => getenv('MYSQL_PASSWORD'),
                                  'charset' => 'utf8mb4',
                                  'driverOptions' => [
                                      PDO::MYSQL_ATTR_SSL_CA => '/tmp/rds-ca-2019-root.pem',
                                      PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                                      PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
                                  ]
                              ],
                          ],
                      ],
                      'migrations_configuration' => [
                          'orm_default' => [
                              'table_storage' => [
                                  'table_name' => 'migrations',
                                  'version_column_length' => 255,
                              ],
                              'migrations_paths' => [
                                  'ArchAssets\Data\Migrations' => 'module/ArchAssets/data/migrations',
                              ],
                          ],
                      ],
                  ],
                  'customerId' => getenv('CUSTOMER_ID'),
                  'appHost' => getenv('APP_HOST'),
                  'appEnvironment' => getenv('APP_ENVIRONMENT'),
                  'databaseName' => [
                      'production' => getenv('MYSQL_DATABASE'),
                      'development' => getenv('MYSQL_DATABASE'),
                      'phpUnit' => getenv('MYSQL_TEST_DATABASE'),
                  ],
                  'baseSqlFilePath' => __DIR__.'/../../data/architrave_1.45.2.sql',
                  'instance_pre_shared_key' => getenv('INSTANCE_PRE_SHARED_KEY'),
                  'logger' => [
                      'activate' => true,
                      'writer' => 'Stream',
                      'writerOptions' => 'php://stderr',
                  ],
                  'searchService' => [
                      'es_host' => getenv('ES_HOST'),
                      'es_index' => getenv('ES_INDEX')
                  ],
                  'email' => [
                      'from' => [
                          'name' => 'Sysadmin Architrave.de',
                          'email' => '<EMAIL>',
                      ],
                  ],
                  'system' => [
                      'api_key_user_email' => getenv('API_KEY_USER_EMAIL'),
                      'scim_user_email' => getenv('SCIM_USER_EMAIL'),
                  ],
                  'slm_mail' => [
                      'mandrill' => [
                          'key' => getenv('MANDRILL_KEY'),
                      ],
                      'http_options' => [
                          'verify_peer' => true,
                          'sslcapath' => '/etc/ssl/certs',
                      ],
                      'testMode' => false,
                  ],
                  'notifications' => [
                      'from' => [
                          'name' => 'Sysadmin Architrave.de',
                          'email' => '<EMAIL>',
                      ],
                      'architraveSupportEmail' => '<EMAIL>',
                      'customer' => getenv('CUSTOMER_NAME'),
                      'customerAdmin' => getenv('CUSTOMER_ADMIN_EMAIL'),
                      'customerSupportEmail' => getenv('CUSTOMER_SUPPORT_EMAIL'),
                  ],
                  'dir' => [
                      'assets' => realpath('/storage/clear/assets'),
                      'export' => realpath('/storage/clear/tmp'),
                      'tmp' => realpath('/storage/clear/tmp'),
                      'sftp' => realpath('/storage/clear/import'),
                      'import' => realpath('/storage/clear/ims-import'),
                      'delphiTranslations' => __DIR__.'/../../clear/delphiTranslations',
                      'quarantine' => realpath('/storage/clear/quarantine'),
                  ],
                  'maintenance_mode' => getenv('MAINTENANCE_MODE') ? getenv('MAINTENANCE_MODE') : 'disabled',
                  'queue' => [
                      'broker' => [
                          'host' => getenv('RABBITMQ_HOST'),
                          'username' => getenv('RABBITMQ_USER'),
                          'password' => getenv('RABBITMQ_PASSWORD'),
                          'port' => 5672,
                          'path' => '/',
                      ],
                      'default_worker_queue' => 'job_queue',
                  ],
                  's3_object_storage' => [
                      'key' => getenv('AV_AUTOMATE_S3_KEY'),
                      'secret' => getenv('AV_AUTOMATE_S3_SECRET_KEY'),
                      'bucket' => getenv('AV_AUTOMATE_S3_BUCKET'),
                      'endpoint' => getenv('AV_AUTOMATE_S3_ENDPOINT'),
                      'region' => getenv('AV_AUTOMATE_S3_REGION'),
                  ],
                  'lmcuser' => [
                      'password_cost' => 4,
                  ],
                  'micro_frontend_application' => [
                      'advanced_uploader' => getenv('ADVANCED_UPLOADER_URL'),
                  ],
                  'heap_app_id' => 3750074421,
                  'google_api_key' => getenv('GOOGLE_API_KEY') ?: 'AIzaSyCp3MI1WH9jsMXcscfmhO98eMy0CKKdgnY',
              ];

              $config['doctrine'] = array_merge_recursive(
                  $config['doctrine'],
                  [
                      'cache' => [
                          'class' => 'Doctrine\Common\Cache\ApcuCache',
                      ],
                      'configuration' => [
                          'orm_default' => [
                              'metadata_cache' => 'apcu',
                              'query_cache' => 'apcu',
                              'result_cache' => 'apcu',
                              'generate_proxies' => false,
                          ],
                      ],
                  ]
              );

              return $config;
              EOF
              echo "Creating required directories..."
              mkdir -p /storage/clear/assets
              mkdir -p /storage/clear/tmp
              mkdir -p /storage/clear/import
              mkdir -p /storage/clear/ims-import
              mkdir -p /storage/clear/quarantine
              chmod -R 777 /storage/clear
              chown -R www-data:www-data /storage/clear
              echo "local.php configuration created successfully"
              ls -la /storage/ArchAssets/config/autoload/
          envFrom:
            - configMapRef:
                name: {{ .Values.tenant.id }}-webapp-env
            - secretRef:
                name: {{ .Values.tenant.id }}-webapp-secrets
          volumeMounts:
            - mountPath: /storage/clear
              name: s3-storage


      containers:
        - name: webapp
          image: {{ .Values.images.webapp }}
          command: ["/bin/sh"]
          args:
            - -c
            - |
              echo "Starting PHP-FPM for tenant {{ .Values.tenant.id }}"

              # Create the correct local.php configuration with SSL
              echo "Creating local.php configuration with SSL support..."
              mkdir -p /storage/ArchAssets/config/autoload
              cat > /storage/ArchAssets/config/autoload/local.php << 'EOF'
              <?php

              $config = [
                  'doctrine' => [
                      'connection' => [
                          'orm_default' => [
                              'driverClass' => 'Doctrine\DBAL\Driver\PDOMySql\Driver',
                              'params' => [
                                  'host' => getenv('MYSQL_HOST'),
                                  'port' => '3306',
                                  'user' => getenv('MYSQL_USER'),
                                  'dbname' => getenv('MYSQL_DATABASE'),
                                  'password' => getenv('MYSQL_PASSWORD'),
                                  'charset' => 'utf8mb4',
                                  'driverOptions' => [
                                      PDO::MYSQL_ATTR_SSL_CA => '/tmp/rds-ca-2019-root.pem',
                                      PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                                      PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
                                  ]
                              ],
                          ],
                      ],
                      'migrations_configuration' => [
                          'orm_default' => [
                              'table_storage' => [
                                  'table_name' => 'migrations',
                                  'version_column_length' => 255,
                              ],
                              'migrations_paths' => [
                                  'ArchAssets\Data\Migrations' => 'module/ArchAssets/data/migrations',
                              ],
                          ],
                      ],
                  ],
                  'customerId' => getenv('CUSTOMER_ID'),
                  'appHost' => getenv('APP_HOST'),
                  'appEnvironment' => getenv('APP_ENVIRONMENT'),
                  'databaseName' => [
                      'production' => getenv('MYSQL_DATABASE'),
                      'development' => getenv('MYSQL_DATABASE'),
                      'phpUnit' => getenv('MYSQL_TEST_DATABASE'),
                  ],
                  'baseSqlFilePath' => __DIR__.'/../../data/architrave_1.45.2.sql',
                  'instance_pre_shared_key' => getenv('INSTANCE_PRE_SHARED_KEY'),
                  'logger' => [
                      'activate' => true,
                      'writer' => 'Stream',
                      'writerOptions' => 'php://stderr',
                  ],
                  'searchService' => [
                      'es_host' => getenv('ES_HOST'),
                      'es_index' => getenv('ES_INDEX')
                  ],
                  'email' => [
                      'from' => [
                          'name' => 'Sysadmin Architrave.de',
                          'email' => '<EMAIL>',
                      ],
                  ],
                  'system' => [
                      'api_key_user_email' => getenv('API_KEY_USER_EMAIL'),
                      'scim_user_email' => getenv('SCIM_USER_EMAIL'),
                  ],
                  'slm_mail' => [
                      'mandrill' => [
                          'key' => getenv('MANDRILL_KEY'),
                      ],
                      'http_options' => [
                          'verify_peer' => true,
                          'sslcapath' => '/etc/ssl/certs',
                      ],
                      'testMode' => false,
                  ],
                  'notifications' => [
                      'from' => [
                          'name' => 'Sysadmin Architrave.de',
                          'email' => '<EMAIL>',
                      ],
                      'architraveSupportEmail' => '<EMAIL>',
                      'customer' => getenv('CUSTOMER_NAME'),
                      'customerAdmin' => getenv('CUSTOMER_ADMIN_EMAIL'),
                      'customerSupportEmail' => getenv('CUSTOMER_SUPPORT_EMAIL'),
                  ],
                  'dir' => [
                      'assets' => realpath('/storage/clear/assets'),
                      'export' => realpath('/storage/clear/tmp'),
                      'tmp' => realpath('/storage/clear/tmp'),
                      'sftp' => realpath('/storage/clear/import'),
                      'import' => realpath('/storage/clear/ims-import'),
                      'delphiTranslations' => __DIR__.'/../../clear/delphiTranslations',
                      'quarantine' => realpath('/storage/clear/quarantine'),
                  ],
                  'maintenance_mode' => getenv('MAINTENANCE_MODE') ? getenv('MAINTENANCE_MODE') : 'disabled',
                  'queue' => [
                      'broker' => [
                          'host' => getenv('RABBITMQ_HOST'),
                          'username' => getenv('RABBITMQ_USER'),
                          'password' => getenv('RABBITMQ_PASSWORD'),
                          'port' => 5672,
                          'path' => '/',
                      ],
                      'default_worker_queue' => 'job_queue',
                  ],
                  's3_object_storage' => [
                      'key' => getenv('AV_AUTOMATE_S3_KEY'),
                      'secret' => getenv('AV_AUTOMATE_S3_SECRET_KEY'),
                      'bucket' => getenv('AV_AUTOMATE_S3_BUCKET'),
                      'endpoint' => getenv('AV_AUTOMATE_S3_ENDPOINT'),
                      'region' => getenv('AV_AUTOMATE_S3_REGION'),
                  ],
                  'lmcuser' => [
                      'password_cost' => 4,
                  ],
                  'micro_frontend_application' => [
                      'advanced_uploader' => getenv('ADVANCED_UPLOADER_URL'),
                  ],
                  'heap_app_id' => 3750074421,
                  'google_api_key' => getenv('GOOGLE_API_KEY') ?: 'AIzaSyCp3MI1WH9jsMXcscfmhO98eMy0CKKdgnY',
              ];

              $config['doctrine'] = array_merge_recursive(
                  $config['doctrine'],
                  [
                      'cache' => [
                          'class' => 'Doctrine\Common\Cache\ApcuCache',
                      ],
                      'configuration' => [
                          'orm_default' => [
                              'metadata_cache' => 'apcu',
                              'query_cache' => 'apcu',
                              'result_cache' => 'apcu',
                              'generate_proxies' => false,
                          ],
                      ],
                  ]
              );

              return $config;
              EOF

              echo "SSL-enabled local.php configuration created successfully"

              # Start PHP-FPM in foreground mode
              exec php-fpm --nodaemonize --force-stderr
          envFrom:
            - configMapRef:
                name: {{ .Values.tenant.id }}-webapp-env
            - secretRef:
                name: {{ .Values.tenant.id }}-webapp-secrets
          ports:
            - containerPort: 9000
              name: php-fpm
              protocol: TCP
          {{- if .Values.webapp.resources }}
          {{- include "architrave-tenant.resources" (list . "webapp") | nindent 10 }}
          {{- end }}
          volumeMounts:
            - mountPath: /storage/clear
              name: s3-storage
            - mountPath: /storage/ArchAssets/data/DoctrineORMModule
              name: doctrine-proxies
            - name: previewchunkedsprites
              mountPath: /storage/clear/assets/previewChunkedSprites
            - mountPath: /tmp
              name: ssl-cert-volume

          livenessProbe:
            tcpSocket:
              port: 9000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: 9000
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
        - name: nginx
          image: {{ .Values.images.nginx }}
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          env:
            - name: TENANT_ID
              value: {{ .Values.tenant.id | quote }}
            - name: TENANT_DOMAIN
              value: {{ .Values.tenant.domain | quote }}
          volumeMounts:
            - mountPath: /etc/nginx/conf.d
              name: nginx-config-volume
            - mountPath: /storage/clear
              name: s3-storage
          livenessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            tcpSocket:
              port: 8080
            initialDelaySeconds: 15
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3

      restartPolicy: Always
      volumes:
        - name: doctrine-proxies
          emptyDir: {}
        {{- if .Values.storage.s3.enabled }}
        - name: s3-storage
          persistentVolumeClaim:
            claimName: {{ .Values.tenant.id }}-s3-pvc
        {{- else }}
        - name: s3-storage
          emptyDir: {}
        {{- end }}
        - name: previewchunkedsprites
          emptyDir: {}
        - name: ssl-cert-volume
          emptyDir: {}
        - name: nginx-config-volume
          emptyDir: {}

      {{- with .Values.webapp.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.webapp.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
