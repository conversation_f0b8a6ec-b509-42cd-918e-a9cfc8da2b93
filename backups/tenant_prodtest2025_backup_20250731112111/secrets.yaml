apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: ""
    DB_PASSWORD: UDdPSnZRTDloKzNWVFBrZyU=
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_VERIFY: ZmFsc2U=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Secret","metadata":{"annotations":{},"name":"prodtest2025-db-secret","namespace":"tenant-prodtest2025"},"stringData":{"DB_HOST":"production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com","DB_NAME":"","DB_PASSWORD":"P7OJvQL9h+3VTPkg%","DB_PORT":"3306","DB_SSL":"true","DB_SSL_CA":"/tmp/rds-ca-2019-root.pem","DB_SSL_VERIFY":"false","DB_USER":"admin"},"type":"Opaque"}
    creationTimestamp: "2025-07-24T07:53:53Z"
    name: prodtest2025-db-secret
    namespace: tenant-prodtest2025
    resourceVersion: "4882721"
    uid: bdf34553-02a9-4fba-bc64-e2d62752c66b
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
