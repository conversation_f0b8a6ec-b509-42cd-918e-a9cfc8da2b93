apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: dGVuYW50X3Rlc3Rza2lwZG5zMTc1MzQ0MzkwNg==
    DB_PASSWORD: UDdPSnZRTDloKzNWVFBrZyU=
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_SSL_VERIFY: ZmFsc2U=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"dGVuYW50X3Rlc3Rza2lwZG5zMTc1MzQ0MzkwNg==","DB_PASSWORD":"UDdPSnZRTDloKzNWVFBrZyU=","DB_PORT":"MzMwNg==","DB_SSL":"dHJ1ZQ==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_SSL_VERIFY":"ZmFsc2U=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"testskipdns1753443906-db-secret","namespace":"tenant-testskipdns1753443906"}}
    creationTimestamp: "2025-07-25T11:50:42Z"
    name: testskipdns1753443906-db-secret
    namespace: tenant-testskipdns1753443906
    resourceVersion: "5523519"
    uid: ac2cbbd9-e1e7-461d-8881-e4af63322b91
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
