apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: dGVuYW50X25ld2N1c3RvbWVyNTAyNQ==
    DB_PASSWORD: UDdPSnZRTDloKzNWVFBrZyU=
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_SSL_VERIFY: ZmFsc2U=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"dGVuYW50X25ld2N1c3RvbWVyNTAyNQ==","DB_PASSWORD":"UDdPSnZRTDloKzNWVFBrZyU=","DB_PORT":"MzMwNg==","DB_SSL":"dHJ1ZQ==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_SSL_VERIFY":"ZmFsc2U=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"newcustomer5025-db-secret","namespace":"tenant-newcustomer5025"}}
    creationTimestamp: "2025-07-30T13:05:37Z"
    name: newcustomer5025-db-secret
    namespace: tenant-newcustomer5025
    resourceVersion: "8152419"
    uid: 9d67646c-cf59-4cec-950b-8b16cfcd4afa
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
