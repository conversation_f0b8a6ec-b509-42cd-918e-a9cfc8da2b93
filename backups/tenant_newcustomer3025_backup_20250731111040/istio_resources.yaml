apiVersion: v1
items:
- apiVersion: networking.istio.io/v1
  kind: VirtualService
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.istio.io/v1beta1","kind":"VirtualService","metadata":{"annotations":{},"labels":{"managed-by":"tenant-onboarding","tenant":"newcustomer3025"},"name":"tenant-newcustomer3025-vs","namespace":"tenant-newcustomer3025"},"spec":{"gateways":["istio-system/tenant-gateway"],"hosts":["newcustomer3025.architrave-assets.de"],"http":[{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"match":[{"uri":{"prefix":"/api"}}],"retries":{"attempts":3,"perTryTimeout":"10s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"newcustomer3025-backend-service","port":{"number":8080}}}],"timeout":"30s"},{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"retries":{"attempts":2,"perTryTimeout":"5s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"newcustomer3025-frontend-service","port":{"number":80}}}],"timeout":"15s"}]}}
    creationTimestamp: "2025-07-30T10:51:47Z"
    generation: 1
    labels:
      managed-by: tenant-onboarding
      tenant: newcustomer3025
    name: tenant-newcustomer3025-vs
    namespace: tenant-newcustomer3025
    resourceVersion: "8103014"
    uid: 92ad94cd-7b91-4cca-8309-60de94254dee
  spec:
    gateways:
    - istio-system/tenant-gateway
    hosts:
    - newcustomer3025.architrave-assets.de
    http:
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      match:
      - uri:
          prefix: /api
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: newcustomer3025-backend-service
          port:
            number: 8080
      timeout: 30s
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      retries:
        attempts: 2
        perTryTimeout: 5s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: newcustomer3025-frontend-service
          port:
            number: 80
      timeout: 15s
kind: List
metadata:
  resourceVersion: ""
