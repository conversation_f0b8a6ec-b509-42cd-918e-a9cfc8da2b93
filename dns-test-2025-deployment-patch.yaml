apiVersion: apps/v1
kind: Deployment
metadata:
  name: dns-test-2025-webapp
  namespace: tenant-dns-test-2025
spec:
  template:
    spec:
      serviceAccountName: dns-test-2025-aws-sa
      initContainers:
      - name: bootstrap-db-secret
        image: amazon/aws-cli:latest
        env:
        - name: AWS_REGION
          value: "eu-central-1"
        - name: SECRET_ID
          value: "arn:aws:secretsmanager:eu-central-1:************:secret:production/rds/master-new"
        command:
        - /bin/sh
        - -c
        - |
          echo "🔐 Fetching database credentials from Secrets Manager..."
          if [ -z "$SECRET_ID" ]; then
            echo "❌ ERROR: SECRET_ID environment variable not set"
            exit 1
          fi
          
          # Fetch secret from AWS Secrets Manager
          SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id "$SECRET_ID" --query SecretString --output text)
          if [ $? -ne 0 ]; then
            echo "❌ ERROR: Failed to fetch secret from Secrets Manager"
            exit 1
          fi
          
          # Parse JSON and create local.php config
          DB_HOST=$(echo "$SECRET_JSON" | jq -r '.host // "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"')
          DB_PORT=$(echo "$SECRET_JSON" | jq -r '.port // "3306"')
          DB_USER=$(echo "$SECRET_JSON" | jq -r '.username // "admin"')
          DB_PASSWORD=$(echo "$SECRET_JSON" | jq -r '.password')
          DB_NAME="architrave"
          
          echo "✅ Database credentials fetched successfully"
          echo "DB_HOST: $DB_HOST"
          echo "DB_PORT: $DB_PORT"
          echo "DB_USER: $DB_USER"
          
          # Create local.php configuration
          cat > /shared-config/local.php << EOF
          <?php
          return [
              'db' => [
                  'driver' => 'Pdo_Mysql',
                  'database' => '$DB_NAME',
                  'username' => '$DB_USER',
                  'password' => '$DB_PASSWORD',
                  'hostname' => '$DB_HOST',
                  'port' => '$DB_PORT',
                  'driver_options' => [
                      PDO::MYSQL_ATTR_SSL_CA => '/tmp/rds-ca-2019-root.pem',
                      PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                  ],
              ],
          ];
          EOF
          
          echo "✅ Database configuration written to /shared-config/local.php"
        volumeMounts:
        - name: shared-config
          mountPath: /shared-config
      containers:
      - name: nginx
        volumeMounts:
        - name: s3-storage
          mountPath: /storage/clear
        - name: shared-config
          mountPath: /app/config/autoload
      - name: backend
        volumeMounts:
        - name: s3-storage
          mountPath: /storage/clear
        - name: shared-config
          mountPath: /app/config/autoload
      volumes:
      - name: s3-storage
        persistentVolumeClaim:
          claimName: s3-pvc-dns-test-2025
      - name: shared-config
        emptyDir: {}