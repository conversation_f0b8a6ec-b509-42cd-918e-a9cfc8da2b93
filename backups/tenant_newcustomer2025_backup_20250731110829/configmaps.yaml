apiVersion: v1
items:
- apiVersion: v1
  data:
    root-cert.pem: |
      -----BEGIN CERTIFICATE-----
      MIIC/TCCAeWgAwIBAgIRAK/WUm4L8S5JnAsQev9Qsn8wDQYJKoZIhvcNAQELBQAw
      GDEWMBQGA1UEChMNY2x1c3Rlci5sb2NhbDAeFw0yNTA3MTQxMzE4MTlaFw0zNTA3
      MTIxMzE4MTlaMBgxFjAUBgNVBAoTDWNsdXN0ZXIubG9jYWwwggEiMA0GCSqGSIb3
      DQEBAQUAA4IBDwAwggEKAoIBAQC78/8vDpHoC1hf+eVK1IVfcRtRmPhzcwp15/nd
      Y3c4YkLlDqocgAy9RadUTHxqKdB1axTkfaneqL4Kovxss3d1DoY9VwEoF1QQDtfC
      UcYU9ACBULBZkOB/qdz0mJakVtvlwin7oZ9hv9EBQoiCHOie5IoxFcnvBhKksAj8
      gVgQg20qwj+kem9TwYQIyaBzOt+Ek1iPVZ/NM5ymJb7l1vqoBNzev2xnPg2yt+o0
      DP+uBjQsDL+qW6GiF20MacpfsGypH6IqCR5ZNdN44USKAONDfW2O0Ca2MJ2VBLoC
      U294IvkJFeyfVFk4g8scy7cppCq0Dd1hhTLAlzxGxtKR5S+PAgMBAAGjQjBAMA4G
      A1UdDwEB/wQEAwICBDAPBgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBTR9oLlnVwa
      2FkZj/LZxPnKv0HQPjANBgkqhkiG9w0BAQsFAAOCAQEAdwmh3TkidEMU2AxlCAjg
      dIMcHkiNY7F6s/YlbU2L9xzw9cnm3V5qJjqQmq7qM949C7imdZZY86x/dibBc/SP
      R0BX/4NspttDN2jZrlfMKncaejAV2szhG9U/snUndEe7bwhnWsrmZHaBn8ZZLAEr
      cMjHC7U3IpMZjWA7ICQD7BL4XxP/zAiurTKg//J+N6Y4sFb4vlnGhLMvkPPRYgZk
      Usbty6XfPbluBzTgW9ZoQMllyGwle+neoIkU+e8dWD7P3cSiJmkkgeg3HF2o6Tzm
      OgmpC+4UBGpP2y8ksdPcajiWzU2osPtdA1ZDe1OClqRoT/183imSxCITHpQFn4U3
      SQ==
      -----END CERTIFICATE-----
  kind: ConfigMap
  metadata:
    creationTimestamp: "2025-07-30T14:55:10Z"
    labels:
      istio.io/config: "true"
    name: istio-ca-root-cert
    namespace: tenant-newcustomer2025
    resourceVersion: "8194367"
    uid: bf5bffc5-f33c-4456-8437-dfd680a4939f
- apiVersion: v1
  data:
    ca.crt: |
      -----BEGIN CERTIFICATE-----
      MIIDBTCCAe2gAwIBAgIIZNoUYdFCUKswDQYJKoZIhvcNAQELBQAwFTETMBEGA1UE
      AxMKa3ViZXJuZXRlczAeFw0yNTA3MTQxMTQ1MTlaFw0zNTA3MTIxMTUwMTlaMBUx
      EzARBgNVBAMTCmt1YmVybmV0ZXMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEK
      AoIBAQCopdDRAbKKca6F74CWhxYy2/XZCulgIIz0AJTNVeGPBhuwqagkQT0qFmui
      yelaj7JYqLgAq9wlkdTzcPErwLPrSq3xBGoJs0k6kIrNAS7lahwltRfoDDk7KVU3
      IP2qullI4Lbl55zYRWI6gAVIeVHutRbkIarAQpQGYGdX5HoqOectSoCHvN6gMVn8
      SFXkt8C723/Hp/DIYYSz0R/zKyDdIeHKJDZLDMMM/QKN0Mn2BtxAbAbZRFPAf+BA
      ZZ8DvMCBrt8/rHaWmZnGWy00oeA0PAjPeYiZtWFMVn50EAhjna8H2cT4rH0rYky3
      8TR4KcefMPpHxI0z5W6RT3l8ySQbAgMBAAGjWTBXMA4GA1UdDwEB/wQEAwICpDAP
      BgNVHRMBAf8EBTADAQH/MB0GA1UdDgQWBBRHqEQMlcvdJQW3GWm+r2WN6h82yDAV
      BgNVHREEDjAMggprdWJlcm5ldGVzMA0GCSqGSIb3DQEBCwUAA4IBAQB8rfKmqnqp
      04HsUKv4qodG9R4aUWJ9UnFLhiNSeijbqd9su8HDdnanq3vQUMrV+wJ3btJzRd2K
      FBTI+WcxokNYWVKAhDuk4GrUKC/t2eRGPf3zWNoyhZK0b0w0vtkbCejms240UKAx
      pwIhkB6TGPs8WZIZbGbEwSEBcHz8TSf2JUkhvRKeP9EZ8qGb03BdFY9ZfLEvWtva
      a0a5J4x4l8r1tKo49wQ3BHH43pLqyLUo8kvOGdOA3hNyu5PNqyn0we3D5pQdCOwQ
      QFuymWMYrDDOIut2wHYQHYL/Jt6sw7ypg5j7cz2w3Q1/Enog9+c/Da63zQWKZicT
      qYg3S16FKCNV
      -----END CERTIFICATE-----
  kind: ConfigMap
  metadata:
    annotations:
      kubernetes.io/description: Contains a CA bundle that can be used to verify the
        kube-apiserver when using internal endpoints such as the internal service
        IP or kubernetes.default.svc. No other usage is guaranteed across distributions
        of Kubernetes clusters.
    creationTimestamp: "2025-07-30T14:55:10Z"
    name: kube-root-ca.crt
    namespace: tenant-newcustomer2025
    resourceVersion: "8194368"
    uid: ce462099-305d-4011-9d59-3bd8bf6d3e5f
kind: List
metadata:
  resourceVersion: ""
