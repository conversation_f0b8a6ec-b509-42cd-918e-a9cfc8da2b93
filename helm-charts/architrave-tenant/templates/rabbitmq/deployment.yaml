{{- if .Values.rabbitmq.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenant.id }}-rabbitmq
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: rabbitmq
  annotations:
    owner: architrave
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.rabbitmq.replicas }}
  selector:
    matchLabels:
      {{- include "architrave-tenant.selectorLabels" . | nindent 6 }}
      app: rabbitmq
  template:
    metadata:
      labels:
        {{- include "architrave-tenant.selectorLabels" . | nindent 8 }}
        app: rabbitmq
      annotations:
        owner: architrave
        {{- with .Values.annotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.security.podSecurityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: rabbitmq
          image: {{ .Values.images.rabbitmq }}
          imagePullPolicy: {{ .Values.images.pullPolicy }}
          {{- with .Values.security.securityContext }}
          securityContext:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: amqp
              containerPort: {{ .Values.rabbitmq.ports.amqp }}
              protocol: TCP
            - name: epmd
              containerPort: {{ .Values.rabbitmq.ports.epmd }}
              protocol: TCP
            {{- if .Values.rabbitmq.management.enabled }}
            - name: management
              containerPort: {{ .Values.rabbitmq.ports.management }}
              protocol: TCP
            {{- end }}
          volumeMounts:
            - name: rabbitmq-config
              mountPath: /etc/rabbitmq/rabbit.config
              subPath: rabbit.config
            - name: rabbitmq-config
              mountPath: /etc/rabbitmq/enabled_plugins
              subPath: enabled_plugins
          {{- include "architrave-tenant.resources" (list . "rabbitmq") | nindent 10 }}
          livenessProbe:
            exec:
              command:
                - rabbitmq-diagnostics
                - ping
            initialDelaySeconds: 60
            periodSeconds: 60
            timeoutSeconds: 15
          readinessProbe:
            exec:
              command:
                - rabbitmq-diagnostics
                - ping
            initialDelaySeconds: 20
            periodSeconds: 60
            timeoutSeconds: 10
      volumes:
        - name: rabbitmq-config
          configMap:
            name: {{ .Values.tenant.id }}-rabbitmq-config
      restartPolicy: Always
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
