---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: dns-test-2025-aws-sa
  namespace: tenant-dns-test-2025
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/dns-test-2025-irsa-role
    eks.amazonaws.com/sts-regional-endpoints: "true"
  labels:
    app.kubernetes.io/name: dns-test-2025-service-account
    app.kubernetes.io/instance: dns-test-2025
    app.kubernetes.io/managed-by: advanced-tenant-onboard