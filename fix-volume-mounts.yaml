apiVersion: apps/v1
kind: Deployment
metadata:
  name: dns-test-2025-webapp
  namespace: tenant-dns-test-2025
spec:
  template:
    spec:
      containers:
      - name: webapp
        volumeMounts:
        # Remove the s3-storage mount from /storage/ArchAssets to preserve webapp files
        # - mountPath: /storage/ArchAssets
        #   name: s3-storage
        - mountPath: /storage/ArchAssets/data/DoctrineORMModule
          name: doctrine-proxies
        - mountPath: /storage/ArchAssets/public/assets/previewChunkedSprites
          name: previewchunkedsprites
        - mountPath: /tmp
          name: ssl-cert-volume
        - mountPath: /storage/clear
          name: clear-s3
        # Keep shared-app for compatibility
        - mountPath: /shared-app
          name: shared-app
        workingDir: /storage/ArchAssets
      
      - name: nginx
        volumeMounts:
        - mountPath: /etc/nginx/conf.d
          name: nginx-config-volume
        # Remove the s3-storage mount from /storage/ArchAssets to preserve webapp files
        # - mountPath: /storage/ArchAssets
        #   name: s3-storage
        - mountPath: /storage/clear
          name: clear-s3
        - mountPath: /shared-app
          name: shared-app