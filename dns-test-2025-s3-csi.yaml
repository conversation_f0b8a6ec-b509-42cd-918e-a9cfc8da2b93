---
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: s3-sc-dns-test-2025
provisioner: s3.csi.aws.com
parameters:
  mounter: geesefs
  options: allow-delete,region=eu-central-1,uid=33,gid=33,file-mode=0666,dir-mode=0777,allow-other
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: s3-pv-dns-test-2025
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Delete
  storageClassName: s3-sc-dns-test-2025
  mountOptions:
    - allow-delete
    - region=eu-central-1
    - uid=33
    - gid=33
    - file-mode=0666
    - dir-mode=0777
    - allow-other
  csi:
    driver: s3.csi.aws.com
    volumeHandle: s3-csi-vol-dns-test-2025
    volumeAttributes:
      bucketName: architravetestdb-dns-test-2025
      region: eu-central-1
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: s3-pvc-dns-test-2025
  namespace: tenant-dns-test-2025
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: s3-sc-dns-test-2025
  volumeName: s3-pv-dns-test-2025