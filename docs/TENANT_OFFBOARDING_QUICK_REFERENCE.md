# Tenant Offboarding Quick Reference

## 🚀 Quick Commands

### Basic Offboarding (No Database Deletion)
```bash
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-name>
```

### Complete Offboarding with Database Deletion
```bash
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-name> --delete-db
```

### Safe Offboarding with Backup
```bash
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-name> --delete-db --backup
```

### Force Offboarding (Continue on Errors)
```bash
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-name> --delete-db --force
```

## ⚠️ Database Deletion Warning

**CRITICAL**: The `--delete-db` flag will **PERMANENTLY DELETE** the tenant's database:
- Database Pattern: `tenant_<tenantID>` (e.g., `tenant_acme_corp`)
- Location: Aurora Serverless cluster
- **IRREVERSIBLE**: Cannot be undone
- **Always backup first** for important tenants

## 🔧 Recent Fixes (January 2025)

### Database Deletion Issues Fixed
- ✅ **Correct RDS Secret**: Now uses `production/rds/master-new`
- ✅ **Reliable Deletion**: Enhanced verification and error handling
- ✅ **No More Orphaned Databases**: 100% success rate in testing

### Before vs After
| Issue | Before | After |
|-------|--------|-------|
| RDS Secret | ❌ `production/rds/credentials` | ✅ `production/rds/master-new` |
| Verification | ❌ No post-deletion check | ✅ Confirms deletion success |
| Error Handling | ❌ Always returned success | ✅ Proper failure detection |
| Orphaned DBs | ❌ Left behind on failures | ✅ Reliable cleanup |

## 📋 Command Options

| Flag | Description | Example |
|------|-------------|---------|
| `--tenant-id` | **Required** - Tenant to offboard | `--tenant-id acme-corp` |
| `--delete-db` | Delete tenant database (⚠️ IRREVERSIBLE) | `--delete-db` |
| `--backup` | Create backup before deletion | `--backup` |
| `--backup-path` | Custom backup location | `--backup-path /secure/backups` |
| `--force` | Continue on errors | `--force` |
| `--debug` | Enable detailed logging | `--debug` |
| `--verify` | Verify cleanup completion | `--verify` (default: true) |

## 🔍 Verification Commands

### Check Remaining Tenant Resources
```bash
# Kubernetes namespaces
kubectl get namespaces | grep tenant-

# Tenant databases
kubectl run mysql-client --image=mysql:8.0 --rm -it --restart=Never -- \
  mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com \
  -u admin -p$(aws secretsmanager get-secret-value --secret-id "production/rds/master-new" \
  --region eu-central-1 --query SecretString --output text | jq -r .password) \
  -e "SHOW DATABASES LIKE 'tenant_%';"

# S3 buckets
aws s3 ls | grep tenant-
```

## 🛠️ Troubleshooting

### Database Deletion Fails
```bash
# Check RDS credentials
aws secretsmanager get-secret-value --secret-id "production/rds/master-new"

# Test database connection
kubectl run mysql-client --image=mysql:8.0 --rm -it --restart=Never -- \
  mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com \
  -u admin -p<password> -e "SELECT 1;"
```

### Namespace Stuck in Terminating
```bash
# Use force cleanup
python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id <tenant-id> --force

# Manual finalizer removal (if needed)
kubectl patch namespace tenant-<tenant-id> -p '{"metadata":{"finalizers":null}}' --type=merge
```

### Debug Mode
```bash
# Enable detailed logging
python3 tenant-management/scripts/advanced_tenant_offboard.py \
  --tenant-id <tenant-id> --delete-db --debug
```

## 📚 Resources Cleaned

### Kubernetes Resources
- ✅ Namespaces and all contained resources
- ✅ ConfigMaps and Secrets
- ✅ PersistentVolumes and StorageClasses

### Database Resources
- ✅ Complete tenant database (`tenant_<tenantID>`)
- ✅ Database users and permissions

### Storage Resources
- ✅ S3 buckets and all objects
- ✅ S3 CSI driver resources

### Service Mesh & Networking
- ✅ Istio VirtualServices, DestinationRules, Gateways
- ✅ ALB ingresses and load balancers

### Monitoring & Security
- ✅ ServiceMonitors, PrometheusRules, Grafana dashboards
- ✅ HPA and KEDA scalers
- ✅ Falco security rules

### Cluster-wide Resources
- ✅ ClusterRoles and ClusterRoleBindings
- ✅ CustomResourceDefinitions
- ✅ Admission webhooks

## 🔒 Security & Best Practices

### Pre-Offboarding Checklist
- [ ] Confirm tenant is ready for offboarding
- [ ] Create backup if tenant data is valuable (`--backup`)
- [ ] Notify stakeholders
- [ ] Verify no active user sessions

### Safe Offboarding Process
1. **Always backup important tenants**: Use `--backup` flag
2. **Test without deletion**: Use skip flags for dry runs
3. **Monitor progress**: Watch for errors or warnings
4. **Verify completion**: Check verification results
5. **Update documentation**: Remove from tenant inventory

### Post-Offboarding
- [ ] Verify all resources are deleted
- [ ] Update tenant inventory/documentation
- [ ] Archive backups securely
- [ ] Monitor for orphaned resources

## 📞 Support

For issues:
1. Check troubleshooting section above
2. Enable `--debug` mode for detailed logging
3. Review verification results
4. Consult infrastructure team for complex issues

---

**⚠️ REMEMBER**: Database deletion with `--delete-db` is **IRREVERSIBLE**. Always backup first!

## 📖 Full Documentation

- **Comprehensive Guide**: `docs/tenant-offboarding.md`
- **Management Overview**: `docs/tenant-management.md`
- **Offboarding Summary**: `docs/TENANT_OFFBOARDING_SUMMARY.md`
