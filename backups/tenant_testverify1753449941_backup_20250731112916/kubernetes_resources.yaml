apiVersion: v1
items:
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-25T15:30:32+02:00"
    creationTimestamp: "2025-07-30T14:16:31Z"
    generateName: testverify1753449941-backend-69756cf7c6-
    labels:
      app: testverify1753449941-backend
      component: backend
      pod-template-hash: 69756cf7c6
      tenant: testverify1753449941
    name: testverify1753449941-backend-69756cf7c6-dvnf6
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: testverify1753449941-backend-69756cf7c6
      uid: 9158380f-e221-4c63-9fc5-08b5ef0ca1cb
    resourceVersion: "8180856"
    uid: 379040b9-6349-4364-93e2-ae1e37383731
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: testverify1753449941
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            key: DB_HOST
            name: testverify1753449941-db-secret
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            key: DB_PORT
            name: testverify1753449941-db-secret
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            key: DB_NAME
            name: testverify1753449941-db-secret
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: testverify1753449941-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: testverify1753449941-db-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: testverify1753449941-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: testverify1753449941-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: testverify1753449941-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
      workingDir: /shared-app
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant testverify1753449941..."
        aws s3 sync s3://architravetestdb/testverify1753449941/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-11-167.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: testverify1753449941-s3-service-account
    serviceAccountName: testverify1753449941-s3-service-account
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-x7d92
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:17:04Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:18:29Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:19:33Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:19:33Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:16:31Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://21fcb69b5af7beb05ddb11060a5f4fd63a37c49c4f30d94e158d6ec1aa2b4e4e
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T14:18:29Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://d75d33151633ed0927733109405a6f0e1c4ff2d2d03c64aaaada8e256e768c30
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T14:18:30Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://2c4c482242c7609c14967b1e668c97fbc8152702f60a044f55e046259deb7798
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:a5b482028826a2e109c53eb70c64ca70973c06265347103c7385f115aea772c7
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://2c4c482242c7609c14967b1e668c97fbc8152702f60a044f55e046259deb7798
          exitCode: 0
          finishedAt: "2025-07-30T14:17:06Z"
          reason: Completed
          startedAt: "2025-07-30T14:17:04Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://1f3abbef90b5d7b3960b6f019f80ebc4ab369b093a369d5d5d0441392ad4276b
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://1f3abbef90b5d7b3960b6f019f80ebc4ab369b093a369d5d5d0441392ad4276b
          exitCode: 0
          finishedAt: "2025-07-30T14:17:15Z"
          reason: Completed
          startedAt: "2025-07-30T14:17:14Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://819199b83ff0d905243fe493eac4d00aae08d52118932ed4955cd5de624f6ca6
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://819199b83ff0d905243fe493eac4d00aae08d52118932ed4955cd5de624f6ca6
          exitCode: 0
          finishedAt: "2025-07-30T14:18:20Z"
          reason: Completed
          startedAt: "2025-07-30T14:18:14Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://a406fb5d27609bf4d4871bc8bada141bc45ab92e4e6f0a06052cb18c0e22e19a
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://a406fb5d27609bf4d4871bc8bada141bc45ab92e4e6f0a06052cb18c0e22e19a
          exitCode: 0
          finishedAt: "2025-07-30T14:18:24Z"
          reason: Completed
          startedAt: "2025-07-30T14:18:24Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://90d18359fc779240e561350b343252c41cda745b059541b11bcaca292a2c6c71
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://90d18359fc779240e561350b343252c41cda745b059541b11bcaca292a2c6c71
          exitCode: 0
          finishedAt: "2025-07-30T14:18:25Z"
          reason: Completed
          startedAt: "2025-07-30T14:18:25Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://8bc623df06dae704d205f97011750a3b04f5225a9b5384054576ab9595e7e7bc
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://8bc623df06dae704d205f97011750a3b04f5225a9b5384054576ab9595e7e7bc
          exitCode: 0
          finishedAt: "2025-07-30T14:18:28Z"
          reason: Completed
          startedAt: "2025-07-30T14:18:28Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-x7d92
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-30T14:16:31Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-25T15:30:32+02:00"
    creationTimestamp: "2025-07-31T09:16:18Z"
    generateName: testverify1753449941-backend-69756cf7c6-
    labels:
      app: testverify1753449941-backend
      component: backend
      pod-template-hash: 69756cf7c6
      tenant: testverify1753449941
    name: testverify1753449941-backend-69756cf7c6-t8f9q
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: testverify1753449941-backend-69756cf7c6
      uid: 9158380f-e221-4c63-9fc5-08b5ef0ca1cb
    resourceVersion: "8601812"
    uid: eca3aa24-fa7d-4f78-8e71-c08493449ea4
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: testverify1753449941
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            key: DB_HOST
            name: testverify1753449941-db-secret
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            key: DB_PORT
            name: testverify1753449941-db-secret
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            key: DB_NAME
            name: testverify1753449941-db-secret
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: testverify1753449941-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: testverify1753449941-db-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: testverify1753449941-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: testverify1753449941-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: testverify1753449941-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
      workingDir: /shared-app
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant testverify1753449941..."
        aws s3 sync s3://architravetestdb/testverify1753449941/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-testverify1753449941-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-10-209.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: testverify1753449941-s3-service-account
    serviceAccountName: testverify1753449941-s3-service-account
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-4hhzp
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:16:26Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:16:44Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:17:47Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:17:47Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-31T09:16:18Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://a1aeeced4aa55ec9b3f3002b486dfed3952a2e54064f6abe9824e289be932257
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-31T09:16:45Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://b74513b617fd2be00a596e24f29f962482afefc0bf72c3a1fa27de53181da78f
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-31T09:16:45Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://a0f4eb68a37a3255b432cf065926af9fb77168fa03dbcb61c6377a4b53d6eb89
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:221149af55371db745f5fcc1fd553e5ab3d0ae1c6b5309a44c181e94a1913d65
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://a0f4eb68a37a3255b432cf065926af9fb77168fa03dbcb61c6377a4b53d6eb89
          exitCode: 0
          finishedAt: "2025-07-31T09:16:27Z"
          reason: Completed
          startedAt: "2025-07-31T09:16:25Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://388122b2e35f37080f0181280373a39c75935cd21db73962af5c41ea7a53c612
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://388122b2e35f37080f0181280373a39c75935cd21db73962af5c41ea7a53c612
          exitCode: 0
          finishedAt: "2025-07-31T09:16:30Z"
          reason: Completed
          startedAt: "2025-07-31T09:16:30Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://602221ccad4b3a660fd8be8cb52502667d81657211941c705c854906d5fd385c
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://602221ccad4b3a660fd8be8cb52502667d81657211941c705c854906d5fd385c
          exitCode: 0
          finishedAt: "2025-07-31T09:16:37Z"
          reason: Completed
          startedAt: "2025-07-31T09:16:31Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://bcaf4496af81729bbea284028b23ff025683b41fdb73acff4d43611c0ed323a9
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://bcaf4496af81729bbea284028b23ff025683b41fdb73acff4d43611c0ed323a9
          exitCode: 0
          finishedAt: "2025-07-31T09:16:41Z"
          reason: Completed
          startedAt: "2025-07-31T09:16:41Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://3b1923e34f3308ab76fc781b9464df0d790364f05c24cba4e3b837c4e21bf9c6
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://3b1923e34f3308ab76fc781b9464df0d790364f05c24cba4e3b837c4e21bf9c6
          exitCode: 0
          finishedAt: "2025-07-31T09:16:42Z"
          reason: Completed
          startedAt: "2025-07-31T09:16:41Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://846c76c6e832c1171fba24d0c9222d782b3a651a30b63948d2cbb971cd41bc7f
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://846c76c6e832c1171fba24d0c9222d782b3a651a30b63948d2cbb971cd41bc7f
          exitCode: 0
          finishedAt: "2025-07-31T09:16:43Z"
          reason: Completed
          startedAt: "2025-07-31T09:16:43Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-4hhzp
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-31T09:16:18Z"
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-25T13:30:33Z"
    generateName: testverify1753449941-frontend-f8d6bc8bc-
    labels:
      app: testverify1753449941-frontend
      component: frontend
      pod-template-hash: f8d6bc8bc
      tenant: testverify1753449941
    name: testverify1753449941-frontend-f8d6bc8bc-2sw5q
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: testverify1753449941-frontend-f8d6bc8bc
      uid: ********-4f7d-432f-85aa-fdfb6cdf3855
    resourceVersion: "5559271"
    uid: 898f000c-9c92-4cdd-acec-b576b5fa1e38
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: testverify1753449941
      - name: DOMAIN
        value: architrave-assets.de
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: BACKEND_URL
        value: http://testverify1753449941-backend-service:8080
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        httpGet:
          path: /health
          port: 80
          scheme: HTTP
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 1
      name: frontend
      ports:
      - containerPort: 80
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        httpGet:
          path: /health
          port: 80
          scheme: HTTP
        initialDelaySeconds: 10
        periodSeconds: 10
        successThreshold: 1
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8kp5d
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for tenant testverify1753449941..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 80;
            server_name testverify1753449941.architrave-assets.de localhost;
            root /usr/share/nginx/html;
            index index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # API routes - proxy to backend service
            location /api/ {
                proxy_pass http://testverify1753449941-backend-service:8080/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # Frontend routes - serve static files
            location / {
                try_files $uri $uri/ /index.html;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }

            # Static assets with caching
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1h;
                add_header Cache-Control "public, immutable";
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully for tenant testverify1753449941"
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8kp5d
        readOnly: true
    nodeName: ip-10-0-12-58.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-8kp5d
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:35Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:35Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:47Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:47Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:33Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://47a8d58e3b76aac2e5ccb281e1c90e837cb09ded3d3f25120ee0019d0aee8c8a
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:42618102e5cc405c551c142acd857aaaa953559b63ca02fff46760e34d4ff149
      lastState: {}
      name: frontend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-25T13:30:35Z"
      volumeMounts:
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8kp5d
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: **********
    hostIPs:
    - ip: **********
    initContainerStatuses:
    - containerID: containerd://4b19c586bfd942f77f605b28248f6d6f73af8ab20da295055679d2d7ffdb4964
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://4b19c586bfd942f77f605b28248f6d6f73af8ab20da295055679d2d7ffdb4964
          exitCode: 0
          finishedAt: "2025-07-25T13:30:34Z"
          reason: Completed
          startedAt: "2025-07-25T13:30:34Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8kp5d
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-25T13:30:33Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Pod","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-healthcheck","tenant":"testverify1753449941"},"name":"testverify1753449941-healthcheck","namespace":"tenant-testverify1753449941"},"spec":{"containers":[{"command":["sh","-c","echo \"Starting healthcheck for tenant testverify1753449941...\"\nwhile true; do\n  echo \"Checking frontend service...\"\n  if curl -f -s http://testverify1753449941-frontend-service.tenant-testverify1753449941.svc.cluster.local:80/ \u003e /dev/null 2\u003e\u00261; then\n    echo \"Frontend service is healthy\"\n  else\n    echo \"Frontend service check failed, but continuing...\"\n  fi\n\n  echo \"Checking backend service...\"\n  if curl -f -s http://testverify1753449941-backend-service.tenant-testverify1753449941.svc.cluster.local:8080/ \u003e /dev/null 2\u003e\u00261; then\n    echo \"Backend service is healthy\"\n  else\n    echo \"Backend service check failed, but continuing...\"\n  fi\n\n  echo \"Health check completed, sleeping for 60 seconds...\"\n  sleep 60\ndone\n"],"image":"curlimages/curl:latest","name":"healthcheck","resources":{"limits":{"cpu":"50m","memory":"64Mi"},"requests":{"cpu":"10m","memory":"32Mi"}}}],"restartPolicy":"Always"}}
    creationTimestamp: "2025-07-25T13:30:01Z"
    labels:
      app: testverify1753449941-healthcheck
      tenant: testverify1753449941
    name: testverify1753449941-healthcheck
    namespace: tenant-testverify1753449941
    resourceVersion: "5558902"
    uid: 0f446ac5-aa03-44c3-8c26-1f3febc36646
  spec:
    containers:
    - command:
      - sh
      - -c
      - |
        echo "Starting healthcheck for tenant testverify1753449941..."
        while true; do
          echo "Checking frontend service..."
          if curl -f -s http://testverify1753449941-frontend-service.tenant-testverify1753449941.svc.cluster.local:80/ > /dev/null 2>&1; then
            echo "Frontend service is healthy"
          else
            echo "Frontend service check failed, but continuing..."
          fi

          echo "Checking backend service..."
          if curl -f -s http://testverify1753449941-backend-service.tenant-testverify1753449941.svc.cluster.local:8080/ > /dev/null 2>&1; then
            echo "Backend service is healthy"
          else
            echo "Backend service check failed, but continuing..."
          fi

          echo "Health check completed, sleeping for 60 seconds..."
          sleep 60
        done
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: healthcheck
      resources:
        limits:
          cpu: 50m
          memory: 64Mi
        requests:
          cpu: 10m
          memory: 32Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-s6shk
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-10-127.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-s6shk
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:07Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:01Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:07Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:07Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:30:01Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://115c9c59585d4257bdbc34a7f54b38a275f07c89446b7f1d76103df7b2459264
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: healthcheck
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-25T13:30:06Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-s6shk
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-25T13:30:01Z"
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-25T13:35:35Z"
    generateName: testverify1753449941-rabbitmq-5bd78568b4-
    labels:
      app: testverify1753449941-rabbitmq
      component: rabbitmq
      pod-template-hash: 5bd78568b4
      tenant: testverify1753449941
    name: testverify1753449941-rabbitmq-5bd78568b4-kldj9
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: testverify1753449941-rabbitmq-5bd78568b4
      uid: b097cc3a-8332-41d3-a313-6b115e6244b8
    resourceVersion: "5561345"
    uid: b8ce1bdf-57fd-4797-99c0-f5e225f53ad6
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: testverify1753449941
      - name: RABBITMQ_DEFAULT_USER
        value: guest
      - name: RABBITMQ_DEFAULT_PASS
        value: guest
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imagePullPolicy: IfNotPresent
      name: rabbitmq
      ports:
      - containerPort: 80
        protocol: TCP
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hjffv
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-10-21.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-hjffv
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:35:36Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:35:36Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:35:36Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:35:36Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:35:35Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://c3552883b2c04150f4ef5d91dfc560b46bf70d5ac9d8bd29c331b5ca93dd5072
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev@sha256:a40d91656430a34f8ee2dfbac222d974915ef9d448f7e99ecb4118218a8a84c0
      lastState: {}
      name: rabbitmq
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-25T13:35:36Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hjffv
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: **********
    hostIPs:
    - ip: **********
    phase: Running
    podIP: **********
    podIPs:
    - ip: **********
    qosClass: BestEffort
    startTime: "2025-07-25T13:35:36Z"
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-backend","component":"backend","tenant":"testverify1753449941"},"name":"testverify1753449941-backend-service","namespace":"tenant-testverify1753449941"},"spec":{"ports":[{"name":"http","port":8080,"protocol":"TCP","targetPort":8080}],"selector":{"app":"testverify1753449941-backend","component":"backend","tenant":"testverify1753449941"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-25T13:30:28Z"
    labels:
      app: testverify1753449941-backend
      component: backend
      tenant: testverify1753449941
    name: testverify1753449941-backend-service
    namespace: tenant-testverify1753449941
    resourceVersion: "5559039"
    uid: 2218f622-4665-47c7-8edf-ad1c4812212e
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
    selector:
      app: testverify1753449941-backend
      component: backend
      tenant: testverify1753449941
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-frontend","component":"frontend","tenant":"testverify1753449941"},"name":"testverify1753449941-frontend-service","namespace":"tenant-testverify1753449941"},"spec":{"ports":[{"port":80,"protocol":"TCP","targetPort":80}],"selector":{"app":"testverify1753449941-frontend","component":"frontend","tenant":"testverify1753449941"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-25T13:30:34Z"
    labels:
      app: testverify1753449941-frontend
      component: frontend
      tenant: testverify1753449941
    name: testverify1753449941-frontend-service
    namespace: tenant-testverify1753449941
    resourceVersion: "5559121"
    uid: ef9c18ed-ca53-4cea-9899-671c5d8a68c9
  spec:
    clusterIP: ************
    clusterIPs:
    - ************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 80
      protocol: TCP
      targetPort: 80
    selector:
      app: testverify1753449941-frontend
      component: frontend
      tenant: testverify1753449941
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"testverify1753449941"},"name":"testverify1753449941-rabbitmq-mgmt-service","namespace":"tenant-testverify1753449941"},"spec":{"ports":[{"port":15672,"protocol":"TCP","targetPort":15672}],"selector":{"app":"testverify1753449941-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"testverify1753449941"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-25T13:27:45Z"
    labels:
      app: testverify1753449941-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: testverify1753449941
    name: testverify1753449941-rabbitmq-mgmt-service
    namespace: tenant-testverify1753449941
    resourceVersion: "5559134"
    uid: fd4f6dec-2e1f-43ab-b39b-437c954a4c7b
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 15672
      protocol: TCP
      targetPort: 15672
    selector:
      app: testverify1753449941-rabbitmq
      component: rabbitmq
      tenant: testverify1753449941
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-rabbitmq","component":"rabbitmq","tenant":"testverify1753449941"},"name":"testverify1753449941-rabbitmq-service","namespace":"tenant-testverify1753449941"},"spec":{"ports":[{"port":5672,"protocol":"TCP","targetPort":5672}],"selector":{"app":"testverify1753449941-rabbitmq","component":"rabbitmq","tenant":"testverify1753449941"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-25T13:27:44Z"
    labels:
      app: testverify1753449941-rabbitmq
      component: rabbitmq
      tenant: testverify1753449941
    name: testverify1753449941-rabbitmq-service
    namespace: tenant-testverify1753449941
    resourceVersion: "5557958"
    uid: c73aace5-eba6-4e2a-862f-f25fe0fd4c36
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 5672
      protocol: TCP
      targetPort: 5672
    selector:
      app: testverify1753449941-rabbitmq
      component: rabbitmq
      tenant: testverify1753449941
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "5"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-backend","component":"backend","tenant":"testverify1753449941"},"name":"testverify1753449941-backend","namespace":"tenant-testverify1753449941"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"testverify1753449941-backend","component":"backend","tenant":"testverify1753449941"}},"template":{"metadata":{"labels":{"app":"testverify1753449941-backend","component":"backend","tenant":"testverify1753449941"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"testverify1753449941"},{"name":"DB_HOST","valueFrom":{"secretKeyRef":{"key":"DB_HOST","name":"testverify1753449941-db-secret"}}},{"name":"DB_PORT","valueFrom":{"secretKeyRef":{"key":"DB_PORT","name":"testverify1753449941-db-secret"}}},{"name":"DB_NAME","valueFrom":{"secretKeyRef":{"key":"DB_NAME","name":"testverify1753449941-db-secret"}}},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"DB_USER","name":"testverify1753449941-db-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"DB_PASSWORD","name":"testverify1753449941-db-secret"}}},{"name":"DB_SSL","valueFrom":{"secretKeyRef":{"key":"DB_SSL","name":"testverify1753449941-db-secret"}}},{"name":"DB_SSL_CA","valueFrom":{"secretKeyRef":{"key":"DB_SSL_CA","name":"testverify1753449941-db-secret"}}},{"name":"DB_SSL_VERIFY","valueFrom":{"secretKeyRef":{"key":"DB_SSL_VERIFY","name":"testverify1753449941-db-secret"}}},{"name":"RABBITMQ_URL","value":"amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","livenessProbe":{"initialDelaySeconds":60,"periodSeconds":30,"tcpSocket":{"port":9000}},"name":"backend","ports":[{"containerPort":9000}],"readinessProbe":{"initialDelaySeconds":30,"periodSeconds":30,"tcpSocket":{"port":9000}},"resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}},"volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"},{"mountPath":"/shared-app","name":"shared-app"}]},{"image":"nginx:1.21-alpine","livenessProbe":{"failureThreshold":5,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-liveness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":90,"periodSeconds":60,"successThreshold":1,"timeoutSeconds":10},"name":"nginx","ports":[{"containerPort":8080}],"readinessProbe":{"failureThreshold":10,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-readiness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"timeoutSeconds":5},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"},{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Downloading RDS CA certificate...\"\ncurl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem\necho \"SSL certificate downloaded successfully\"\n"],"image":"curlimages/curl:latest","name":"ssl-cert-downloader","volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"}]},{"command":["sh","-c","echo \"Copying application files from /storage/ArchAssets to shared volume...\"\nif [ -d \"/storage/ArchAssets\" ]; then\n    cp -r /storage/ArchAssets/* /shared-app/ 2\u003e/dev/null || true\n    echo \"Application files copied successfully from /storage/ArchAssets\"\n    echo \"Copied files:\"\n    ls -la /shared-app/ | head -10\nelse\n    echo \"Warning: /storage/ArchAssets directory not found in webapp_dev image\"\n    echo \"Available directories in /storage:\"\n    ls -la /storage/ 2\u003e/dev/null || echo \"No /storage directory\"\n    echo \"Available directories in root:\"\n    ls -la / | grep -E \"(var|storage|app)\" || echo \"No relevant directories found\"\nfi\necho \"Application files copy process completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"app-files-copier","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Fixing PHP application config path issues and PHP-FPM compatibility...\"\n\n# Fix the relative path issue in /shared-app/public/api/index.php\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Found API index.php, fixing config path...\"\n\n  # Create backup\n  cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup\n\n  # Fix the relative path issue by using absolute path\n  sed -i \"s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g\" /shared-app/public/api/index.php\n\n  echo \"Config path fixed in API index.php\"\n  echo \"Checking the fix:\"\n  grep -n \"require.*config.*application.config.php\" /shared-app/public/api/index.php || echo \"Pattern not found after fix\"\nelse\n  echo \"Warning: /shared-app/public/api/index.php not found\"\n  echo \"Available files in /shared-app/public/:\"\n  ls -la /shared-app/public/ 2\u003e/dev/null || echo \"No public directory\"\nfi\n\n# Create PHP-FPM compatibility polyfill for apache_request_headers()\necho \"Creating PHP-FPM compatibility polyfill...\"\ncat \u003e /shared-app/php_fpm_polyfill.php \u003c\u003c 'POLYFILL_EOF'\n\u003c?php\n/**\n * PHP-FPM Compatibility Polyfill for Apache functions\n * This file provides missing Apache functions for PHP-FPM + nginx environments\n *\n * NOTE: Using forced function definition without conditional checks\n * because function_exists() may return false positives in some PHP-FPM environments\n */\n\n/**\n * Polyfill for apache_request_headers() function in PHP-FPM environment\n * Only define if the function doesn't already exist\n */\nif (!function_exists('apache_request_headers')) {\n    function apache_request_headers() {\n        $headers = array();\n\n        // Get all HTTP headers from $_SERVER superglobal\n        foreach ($_SERVER as $key =\u003e $value) {\n            if (strpos($key, 'HTTP_') === 0) {\n                // Convert HTTP_HEADER_NAME to Header-Name format\n                $header = str_replace('_', '-', substr($key, 5));\n                $header = ucwords(strtolower($header), '-');\n                $headers[$header] = $value;\n            }\n        }\n\n        // Add special headers that might not have HTTP_ prefix\n        if (isset($_SERVER['CONTENT_TYPE'])) {\n            $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];\n        }\n        if (isset($_SERVER['CONTENT_LENGTH'])) {\n            $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];\n        }\n\n        return $headers;\n    }\n}\n\n/**\n * Polyfill for apache_response_headers() function in PHP-FPM environment\n * @return array\n */\nfunction apache_response_headers() {\n    $headers = array();\n\n    // Get headers that were set with header() function\n    if (function_exists('headers_list')) {\n        foreach (headers_list() as $header) {\n            $parts = explode(':', $header, 2);\n            if (count($parts) === 2) {\n                $headers[trim($parts[0])] = trim($parts[1]);\n            }\n        }\n    }\n\n    return $headers;\n}\n\n/**\n * Alias for apache_request_headers() - commonly used alternative\n * Only define if the function doesn't already exist\n */\nif (!function_exists('getallheaders')) {\n    function getallheaders() {\n        return apache_request_headers();\n    }\n}\nPOLYFILL_EOF\n\necho \"✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php\"\n\n# Inject the polyfill into the main bootstrap file\nif [ -f \"/shared-app/bootstrap.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into bootstrap.php...\"\n\n  # Create backup\n  cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bootstrap.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into bootstrap.php\"\nelse\n  echo \"Warning: /shared-app/bootstrap.php not found\"\nfi\n\n# Also inject into the API index.php as a fallback\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into API index.php as fallback...\"\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/public/api/index.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into API index.php\"\nfi\n\n# CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)...\"\n\n  # Create backup\n  cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/architrave.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into CLI entry point\"\nelse\n  echo \"Warning: /shared-app/bin/architrave.php not found\"\nfi\n\n# Also check for alternative CLI entry points\nif [ -f \"/shared-app/bin/console.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into console.php...\"\n\n  # Create backup\n  cp /shared-app/bin/console.php /shared-app/bin/console.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/console.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into console.php\"\nfi\n\n# Verify config file exists\nif [ -f \"/shared-app/config/application.config.php\" ]; then\n  echo \"✅ Config file exists at /shared-app/config/application.config.php\"\nelse\n  echo \"❌ Config file missing at /shared-app/config/application.config.php\"\n  echo \"Available files in /shared-app/config/:\"\n  ls -la /shared-app/config/ 2\u003e/dev/null || echo \"No config directory\"\nfi\n\necho \"✅ PHP config path fix and PHP-FPM compatibility setup completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-config-fixer","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Validating PHP environment for PHP-FPM + nginx compatibility...\"\n\n# Test if polyfill was loaded correctly\nphp -r \"\nrequire_once '/shared-app/php_fpm_polyfill.php';\n\necho 'Testing PHP-FPM polyfill functions...\\n';\n\n// Test apache_request_headers function\nif (function_exists('apache_request_headers')) {\n    echo '✅ apache_request_headers() function is available\\n';\n} else {\n    echo '❌ apache_request_headers() function is missing\\n';\n    exit(1);\n}\n\n// Test getallheaders function\nif (function_exists('getallheaders')) {\n    echo '✅ getallheaders() function is available\\n';\n} else {\n    echo '❌ getallheaders() function is missing\\n';\n    exit(1);\n}\n\n// Test apache_response_headers function\nif (function_exists('apache_response_headers')) {\n    echo '✅ apache_response_headers() function is available\\n';\n} else {\n    echo '❌ apache_response_headers() function is missing\\n';\n    exit(1);\n}\n\necho '✅ All PHP-FPM polyfill functions are working correctly\\n';\n\"\n\n# Test basic PHP application loading\necho \"Testing PHP application bootstrap loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    echo '✅ Bootstrap loaded successfully\\n';\n} catch (Exception \\$e) {\n    echo '❌ Bootstrap loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# Test Laminas application config loading\necho \"Testing Laminas application config loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    \\$config = require '/shared-app/config/application.config.php';\n    echo '✅ Application config loaded successfully\\n';\n    echo 'Config modules: ' . count(\\$config['modules']) . '\\n';\n} catch (Exception \\$e) {\n    echo '❌ Application config loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# CRITICAL FIX: Test CLI entry point functionality\necho \"Testing CLI entry point functionality...\"\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Testing bin/architrave.php polyfill integration...\"\n  php -d display_errors=1 -d error_reporting=E_ALL -r \"\n  try {\n      // Change to the correct directory\n      chdir('/shared-app');\n\n      // Test if polyfill file exists and is readable\n      if (!file_exists('php_fpm_polyfill.php')) {\n          echo '❌ Polyfill file not found\\n';\n          exit(1);\n      }\n\n      // Load polyfill manually to test\n      require_once 'php_fpm_polyfill.php';\n\n      // Test if functions are available\n      if (function_exists('apache_request_headers')) {\n          echo '✅ CLI polyfill functions available\\n';\n      } else {\n          echo '❌ CLI polyfill functions missing\\n';\n          exit(1);\n      }\n\n      echo '✅ CLI environment validation successful\\n';\n  } catch (Exception \\$e) {\n      echo '⚠️ CLI test failed: ' . \\$e-\u003egetMessage() . '\\n';\n      echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n      echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n      // Don't exit with error as this might fail in init container context\n  }\n  \" || echo \"⚠️ CLI test completed with warnings (normal in init container)\"\nelse\n  echo \"⚠️ CLI entry point not found - will be available after application sync\"\nfi\n\necho \"✅ PHP environment validation completed successfully\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-env-validator","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Creating HTTP nginx configuration for port 8080...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 8080;\n    server_name localhost;\n    root /shared-app/public;\n    index index.php index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /api/health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # Root endpoint - redirect to API\n    location = / {\n        return 302 /api/;\n    }\n\n    location / {\n        try_files $uri $uri/ /api/index.php?$args;\n    }\n\n    location ~ \\.php$ {\n        fastcgi_pass localhost:9000;\n        fastcgi_index index.php;\n        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\n        fastcgi_param AUTOMATED_TEST 1;\n        include fastcgi_params;\n        fastcgi_read_timeout 310;\n    }\n\n    location ~ /\\.ht {\n        deny all;\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"ssl-certs"},{"emptyDir":{},"name":"shared-app"},{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-25T13:27:46Z"
    generation: 6
    labels:
      app: testverify1753449941-backend
      component: backend
      tenant: testverify1753449941
    name: testverify1753449941-backend
    namespace: tenant-testverify1753449941
    resourceVersion: "8601822"
    uid: a5855f47-a3d1-4b8a-a7e4-d7f84c0df6be
  spec:
    progressDeadlineSeconds: 600
    replicas: 2
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: testverify1753449941-backend
        component: backend
        tenant: testverify1753449941
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-25T15:30:32+02:00"
        creationTimestamp: null
        labels:
          app: testverify1753449941-backend
          component: backend
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: testverify1753449941
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: testverify1753449941-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: testverify1753449941-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: testverify1753449941-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: testverify1753449941-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: testverify1753449941-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: testverify1753449941-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: testverify1753449941-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: testverify1753449941-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
          workingDir: /shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant testverify1753449941..."
            aws s3 sync s3://architravetestdb/testverify1753449941/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: testverify1753449941-s3-service-account
        serviceAccountName: testverify1753449941-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    conditions:
    - lastTransitionTime: "2025-07-25T13:27:46Z"
      lastUpdateTime: "2025-07-25T13:48:40Z"
      message: ReplicaSet "testverify1753449941-backend-69756cf7c6" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-31T09:17:47Z"
      lastUpdateTime: "2025-07-31T09:17:47Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 6
    readyReplicas: 2
    replicas: 2
    updatedReplicas: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-frontend","component":"frontend","tenant":"testverify1753449941"},"name":"testverify1753449941-frontend","namespace":"tenant-testverify1753449941"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"testverify1753449941-frontend","component":"frontend","tenant":"testverify1753449941"}},"template":{"metadata":{"labels":{"app":"testverify1753449941-frontend","component":"frontend","tenant":"testverify1753449941"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"testverify1753449941"},{"name":"DOMAIN","value":"architrave-assets.de"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"},{"name":"BACKEND_URL","value":"http://testverify1753449941-backend-service:8080"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl","livenessProbe":{"httpGet":{"path":"/health","port":80},"initialDelaySeconds":30,"periodSeconds":30},"name":"frontend","ports":[{"containerPort":80}],"readinessProbe":{"httpGet":{"path":"/health","port":80},"initialDelaySeconds":10,"periodSeconds":10},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Creating HTTP nginx configuration for tenant testverify1753449941...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 80;\n    server_name testverify1753449941.architrave-assets.de localhost;\n    root /usr/share/nginx/html;\n    index index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # API routes - proxy to backend service\n    location /api/ {\n        proxy_pass http://testverify1753449941-backend-service:8080/api/;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n\n    # Frontend routes - serve static files\n    location / {\n        try_files $uri $uri/ /index.html;\n        add_header Cache-Control \"no-cache, no-store, must-revalidate\";\n        add_header Pragma \"no-cache\";\n        add_header Expires \"0\";\n    }\n\n    # Static assets with caching\n    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully for tenant testverify1753449941\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-25T13:30:33Z"
    generation: 1
    labels:
      app: testverify1753449941-frontend
      component: frontend
      tenant: testverify1753449941
    name: testverify1753449941-frontend
    namespace: tenant-testverify1753449941
    resourceVersion: "5559275"
    uid: cf07429d-dda8-46e6-8f42-0e0fcec85b39
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: testverify1753449941-frontend
        component: frontend
        tenant: testverify1753449941
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: testverify1753449941-frontend
          component: frontend
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: testverify1753449941
          - name: DOMAIN
            value: architrave-assets.de
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://testverify1753449941-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant testverify1753449941..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name testverify1753449941.architrave-assets.de localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://testverify1753449941-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant testverify1753449941"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-25T13:30:47Z"
      lastUpdateTime: "2025-07-25T13:30:47Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2025-07-25T13:30:33Z"
      lastUpdateTime: "2025-07-25T13:30:47Z"
      message: ReplicaSet "testverify1753449941-frontend-f8d6bc8bc" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-rabbitmq","component":"rabbitmq","tenant":"testverify1753449941"},"name":"testverify1753449941-rabbitmq","namespace":"tenant-testverify1753449941"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"testverify1753449941-rabbitmq","component":"rabbitmq","tenant":"testverify1753449941"}},"template":{"metadata":{"labels":{"app":"testverify1753449941-rabbitmq","component":"rabbitmq","tenant":"testverify1753449941"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"testverify1753449941"},{"name":"RABBITMQ_DEFAULT_USER","value":"guest"},{"name":"RABBITMQ_DEFAULT_PASS","value":"guest"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02","name":"rabbitmq","ports":[{"containerPort":80}]}]}}}}
    creationTimestamp: "2025-07-25T13:27:43Z"
    generation: 1
    labels:
      app: testverify1753449941-rabbitmq
      component: rabbitmq
      tenant: testverify1753449941
    name: testverify1753449941-rabbitmq
    namespace: tenant-testverify1753449941
    resourceVersion: "5561353"
    uid: 38f6d3aa-1d36-4b76-aeab-9a5c96bdc188
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: testverify1753449941-rabbitmq
        component: rabbitmq
        tenant: testverify1753449941
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: testverify1753449941-rabbitmq
          component: rabbitmq
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: testverify1753449941
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-25T13:27:44Z"
      lastUpdateTime: "2025-07-25T13:27:45Z"
      message: ReplicaSet "testverify1753449941-rabbitmq-5bd78568b4" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-25T13:35:36Z"
      lastUpdateTime: "2025-07-25T13:35:36Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "4"
    creationTimestamp: "2025-07-25T13:30:42Z"
    generation: 5
    labels:
      app: testverify1753449941-backend
      component: backend
      pod-template-hash: 55bbc6d5d8
      tenant: testverify1753449941
    name: testverify1753449941-backend-55bbc6d5d8
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: testverify1753449941-backend
      uid: a5855f47-a3d1-4b8a-a7e4-d7f84c0df6be
    resourceVersion: "5566571"
    uid: c14e49e9-06aa-4891-b5bc-6b62ba05ad49
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: testverify1753449941-backend
        component: backend
        pod-template-hash: 55bbc6d5d8
        tenant: testverify1753449941
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-25T15:30:32+02:00"
        creationTimestamp: null
        labels:
          app: testverify1753449941-backend
          component: backend
          pod-template-hash: 55bbc6d5d8
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: testverify1753449941
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: testverify1753449941-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: testverify1753449941-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: testverify1753449941-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: testverify1753449941-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: testverify1753449941-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: testverify1753449941-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: testverify1753449941-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: testverify1753449941-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant testverify1753449941..."
            aws s3 sync s3://architravetestdb/testverify1753449941/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: testverify1753449941-s3-service-account
        serviceAccountName: testverify1753449941-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 5
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "3"
    creationTimestamp: "2025-07-25T13:30:32Z"
    generation: 3
    labels:
      app: testverify1753449941-backend
      component: backend
      pod-template-hash: 56d765588d
      tenant: testverify1753449941
    name: testverify1753449941-backend-56d765588d
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: testverify1753449941-backend
      uid: a5855f47-a3d1-4b8a-a7e4-d7f84c0df6be
    resourceVersion: "5559212"
    uid: a4469be7-6bc0-463e-8ef7-56f234d4b23a
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: testverify1753449941-backend
        component: backend
        pod-template-hash: 56d765588d
        tenant: testverify1753449941
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-25T15:30:32+02:00"
        creationTimestamp: null
        labels:
          app: testverify1753449941-backend
          component: backend
          pod-template-hash: 56d765588d
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: testverify1753449941
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: testverify1753449941-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: testverify1753449941-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: testverify1753449941-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: testverify1753449941-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: testverify1753449941-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: testverify1753449941-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: testverify1753449941-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: testverify1753449941-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 3
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-25T13:27:46Z"
    generation: 2
    labels:
      app: testverify1753449941-backend
      component: backend
      pod-template-hash: 65df7799d9
      tenant: testverify1753449941
    name: testverify1753449941-backend-65df7799d9
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: testverify1753449941-backend
      uid: a5855f47-a3d1-4b8a-a7e4-d7f84c0df6be
    resourceVersion: "5559766"
    uid: 4aa6624b-c1cd-4948-8186-a07e41de6faa
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: testverify1753449941-backend
        component: backend
        pod-template-hash: 65df7799d9
        tenant: testverify1753449941
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: testverify1753449941-backend
          component: backend
          pod-template-hash: 65df7799d9
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: testverify1753449941
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: testverify1753449941-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: testverify1753449941-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: testverify1753449941-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: testverify1753449941-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: testverify1753449941-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: testverify1753449941-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: testverify1753449941-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: testverify1753449941-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "5"
    creationTimestamp: "2025-07-25T13:44:57Z"
    generation: 2
    labels:
      app: testverify1753449941-backend
      component: backend
      pod-template-hash: 69756cf7c6
      tenant: testverify1753449941
    name: testverify1753449941-backend-69756cf7c6
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: testverify1753449941-backend
      uid: a5855f47-a3d1-4b8a-a7e4-d7f84c0df6be
    resourceVersion: "8601816"
    uid: 9158380f-e221-4c63-9fc5-08b5ef0ca1cb
  spec:
    replicas: 2
    selector:
      matchLabels:
        app: testverify1753449941-backend
        component: backend
        pod-template-hash: 69756cf7c6
        tenant: testverify1753449941
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-25T15:30:32+02:00"
        creationTimestamp: null
        labels:
          app: testverify1753449941-backend
          component: backend
          pod-template-hash: 69756cf7c6
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: testverify1753449941
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: testverify1753449941-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: testverify1753449941-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: testverify1753449941-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: testverify1753449941-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: testverify1753449941-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: testverify1753449941-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: testverify1753449941-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: testverify1753449941-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
          workingDir: /shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant testverify1753449941..."
            aws s3 sync s3://architravetestdb/testverify1753449941/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: testverify1753449941-s3-service-account
        serviceAccountName: testverify1753449941-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    fullyLabeledReplicas: 2
    observedGeneration: 2
    readyReplicas: 2
    replicas: 2
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-25T13:29:24Z"
    generation: 2
    labels:
      app: testverify1753449941-backend
      component: backend
      pod-template-hash: 6cbdd69bd9
      tenant: testverify1753449941
    name: testverify1753449941-backend-6cbdd69bd9
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: testverify1753449941-backend
      uid: a5855f47-a3d1-4b8a-a7e4-d7f84c0df6be
    resourceVersion: "5559083"
    uid: dd8d1ca0-ebef-4b02-ab71-19bd03920054
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: testverify1753449941-backend
        component: backend
        pod-template-hash: 6cbdd69bd9
        tenant: testverify1753449941
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-25T15:29:24+02:00"
        creationTimestamp: null
        labels:
          app: testverify1753449941-backend
          component: backend
          pod-template-hash: 6cbdd69bd9
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: testverify1753449941
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: testverify1753449941-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: testverify1753449941-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: testverify1753449941-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: testverify1753449941-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: testverify1753449941-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: testverify1753449941-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: testverify1753449941-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: testverify1753449941-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@testverify1753449941-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-25T13:30:33Z"
    generation: 1
    labels:
      app: testverify1753449941-frontend
      component: frontend
      pod-template-hash: f8d6bc8bc
      tenant: testverify1753449941
    name: testverify1753449941-frontend-f8d6bc8bc
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: testverify1753449941-frontend
      uid: cf07429d-dda8-46e6-8f42-0e0fcec85b39
    resourceVersion: "5559274"
    uid: ********-4f7d-432f-85aa-fdfb6cdf3855
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: testverify1753449941-frontend
        component: frontend
        pod-template-hash: f8d6bc8bc
        tenant: testverify1753449941
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: testverify1753449941-frontend
          component: frontend
          pod-template-hash: f8d6bc8bc
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: testverify1753449941
          - name: DOMAIN
            value: architrave-assets.de
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://testverify1753449941-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant testverify1753449941..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name testverify1753449941.architrave-assets.de localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://testverify1753449941-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant testverify1753449941"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-25T13:27:44Z"
    generation: 1
    labels:
      app: testverify1753449941-rabbitmq
      component: rabbitmq
      pod-template-hash: 5bd78568b4
      tenant: testverify1753449941
    name: testverify1753449941-rabbitmq-5bd78568b4
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: testverify1753449941-rabbitmq
      uid: 38f6d3aa-1d36-4b76-aeab-9a5c96bdc188
    resourceVersion: "5561350"
    uid: b097cc3a-8332-41d3-a313-6b115e6244b8
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: testverify1753449941-rabbitmq
        component: rabbitmq
        pod-template-hash: 5bd78568b4
        tenant: testverify1753449941
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: testverify1753449941-rabbitmq
          component: rabbitmq
          pod-template-hash: 5bd78568b4
          tenant: testverify1753449941
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: testverify1753449941
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"keda.sh/v1alpha1","kind":"ScaledObject","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-backend","tenant":"testverify1753449941"},"name":"testverify1753449941-backend-scaler","namespace":"tenant-testverify1753449941"},"spec":{"advanced":{"horizontalPodAutoscalerConfig":{"behavior":{"scaleDown":{"policies":[{"periodSeconds":60,"type":"Percent","value":50}],"stabilizationWindowSeconds":300},"scaleUp":{"policies":[{"periodSeconds":30,"type":"Percent","value":100},{"periodSeconds":30,"type":"Pods","value":2}],"selectPolicy":"Max","stabilizationWindowSeconds":0}}}},"cooldownPeriod":300,"maxReplicaCount":20,"minReplicaCount":2,"pollingInterval":15,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"testverify1753449941-backend"},"triggers":[{"metadata":{"value":"70"},"metricType":"Utilization","type":"cpu"},{"metadata":{"value":"80"},"metricType":"Utilization","type":"memory"},{"metadata":{"metricName":"http_requests_per_second","query":"sum(rate(istio_requests_total{destination_service_name=\"testverify1753449941-backend\"}[2m]))","serverAddress":"http://prometheus-server.monitoring.svc.cluster.local:9090","threshold":"50"},"type":"prometheus"},{"metadata":{"host":"testverify1753449941-rabbitmq.tenant-testverify1753449941.svc.cluster.local","mode":"QueueLength","password":"guest","port":"15672","protocol":"http","queueName":"tenant-queue","username":"guest","value":"5"},"type":"rabbitmq"}]}}
    creationTimestamp: "2025-07-25T13:32:30Z"
    labels:
      app: testverify1753449941-backend
      app.kubernetes.io/managed-by: keda-operator
      app.kubernetes.io/name: keda-hpa-testverify1753449941-backend-scaler
      app.kubernetes.io/part-of: testverify1753449941-backend-scaler
      app.kubernetes.io/version: 2.17.2
      scaledobject.keda.sh/name: testverify1753449941-backend-scaler
      tenant: testverify1753449941
    name: keda-hpa-testverify1753449941-backend-scaler
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: keda.sh/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: ScaledObject
      name: testverify1753449941-backend-scaler
      uid: e390ac8d-938e-4940-b5e4-21134311c4cc
    resourceVersion: "8606826"
    uid: 97f54aa0-e896-4429-94c0-0eb46e948342
  spec:
    behavior:
      scaleDown:
        policies:
        - periodSeconds: 60
          type: Percent
          value: 50
        selectPolicy: Max
        stabilizationWindowSeconds: 300
      scaleUp:
        policies:
        - periodSeconds: 30
          type: Percent
          value: 100
        - periodSeconds: 30
          type: Pods
          value: 2
        selectPolicy: Max
        stabilizationWindowSeconds: 0
    maxReplicas: 20
    metrics:
    - external:
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: testverify1753449941-backend-scaler
        target:
          averageValue: "50"
          type: AverageValue
      type: External
    - external:
        metric:
          name: s3-rabbitmq-tenant-queue
          selector:
            matchLabels:
              scaledobject.keda.sh/name: testverify1753449941-backend-scaler
        target:
          averageValue: "5"
          type: AverageValue
      type: External
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 2
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: testverify1753449941-backend
  status:
    conditions:
    - lastTransitionTime: "2025-07-25T13:32:45Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-31T09:17:09Z"
      message: 'the HPA was unable to compute the replica count: unable to get external
        metric tenant-testverify1753449941/s3-rabbitmq-tenant-queue/&LabelSelector{MatchLabels:map[string]string{scaledobject.keda.sh/name:
        testverify1753449941-backend-scaler,},MatchExpressions:[]LabelSelectorRequirement{},}:
        unable to fetch metrics from external metrics API: rpc error: code = Unknown
        desc = error when getting metric values metric:s3-rabbitmq-tenant-queue encountered
        error'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-07-25T13:33:15Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
    currentMetrics:
    - external:
        current:
          averageValue: "0"
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: testverify1753449941-backend-scaler
      type: External
    - type: ""
    - resource:
        current:
          averageUtilization: 1
          averageValue: 1m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 5
          averageValue: "18962432"
        name: memory
      type: Resource
    currentReplicas: 2
    desiredReplicas: 2
    lastScaleTime: "2025-07-25T13:32:45Z"
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"keda.sh/v1alpha1","kind":"ScaledObject","metadata":{"annotations":{},"labels":{"app":"testverify1753449941-frontend","tenant":"testverify1753449941"},"name":"testverify1753449941-frontend-scaler","namespace":"tenant-testverify1753449941"},"spec":{"cooldownPeriod":300,"maxReplicaCount":10,"minReplicaCount":1,"pollingInterval":15,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"testverify1753449941-frontend"},"triggers":[{"metadata":{"value":"70"},"metricType":"Utilization","type":"cpu"},{"metadata":{"value":"80"},"metricType":"Utilization","type":"memory"},{"metadata":{"metricName":"http_requests_per_second","query":"sum(rate(istio_requests_total{destination_service_name=\"testverify1753449941-frontend\"}[2m]))","serverAddress":"http://prometheus-server.monitoring.svc.cluster.local:9090","threshold":"30"},"type":"prometheus"}]}}
    creationTimestamp: "2025-07-25T13:32:31Z"
    labels:
      app: testverify1753449941-frontend
      app.kubernetes.io/managed-by: keda-operator
      app.kubernetes.io/name: keda-hpa-testverify1753449941-frontend-scaler
      app.kubernetes.io/part-of: testverify1753449941-frontend-scaler
      app.kubernetes.io/version: 2.17.2
      scaledobject.keda.sh/name: testverify1753449941-frontend-scaler
      tenant: testverify1753449941
    name: keda-hpa-testverify1753449941-frontend-scaler
    namespace: tenant-testverify1753449941
    ownerReferences:
    - apiVersion: keda.sh/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: ScaledObject
      name: testverify1753449941-frontend-scaler
      uid: d7c056d5-525a-4e28-b2f0-cfd5baaab9bf
    resourceVersion: "8605481"
    uid: b473c9c4-f04e-4cf3-8a6f-0b6959eb6247
  spec:
    maxReplicas: 10
    metrics:
    - external:
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: testverify1753449941-frontend-scaler
        target:
          averageValue: "30"
          type: AverageValue
      type: External
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 1
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: testverify1753449941-frontend
  status:
    conditions:
    - lastTransitionTime: "2025-07-25T13:32:46Z"
      message: recommended size matches current size
      reason: ReadyForNewScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-25T13:36:02Z"
      message: the HPA was able to successfully calculate a replica count from cpu
        resource utilization (percentage of request)
      reason: ValidMetricFound
      status: "True"
      type: ScalingActive
    - lastTransitionTime: "2025-07-25T13:32:46Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
    currentMetrics:
    - external:
        current:
          averageValue: "0"
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: testverify1753449941-frontend-scaler
      type: External
    - resource:
        current:
          averageUtilization: 2
          averageValue: 1m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 4
          averageValue: "3252224"
        name: memory
      type: Resource
    currentReplicas: 1
    desiredReplicas: 1
kind: List
metadata:
  resourceVersion: ""
