apiVersion: v1
items:
- apiVersion: networking.istio.io/v1
  kind: VirtualService
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.istio.io/v1beta1","kind":"VirtualService","metadata":{"annotations":{},"labels":{"managed-by":"tenant-onboarding","tenant":"newtesttoday"},"name":"tenant-newtesttoday-vs","namespace":"tenant-newtesttoday"},"spec":{"gateways":["istio-system/tenant-gateway"],"hosts":["newtesttoday.architrave-assets.de"],"http":[{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"match":[{"uri":{"prefix":"/api"}}],"retries":{"attempts":3,"perTryTimeout":"10s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"newtesttoday-backend-service","port":{"number":8080}}}],"timeout":"30s"},{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"retries":{"attempts":2,"perTryTimeout":"5s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"newtesttoday-frontend-service","port":{"number":80}}}],"timeout":"15s"}]}}
    creationTimestamp: "2025-07-30T10:06:14Z"
    generation: 1
    labels:
      managed-by: tenant-onboarding
      tenant: newtesttoday
    name: tenant-newtesttoday-vs
    namespace: tenant-newtesttoday
    resourceVersion: "8085073"
    uid: d4e0ba53-6f4e-4c1b-ae3d-2707bb139726
  spec:
    gateways:
    - istio-system/tenant-gateway
    hosts:
    - newtesttoday.architrave-assets.de
    http:
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      match:
      - uri:
          prefix: /api
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: newtesttoday-backend-service
          port:
            number: 8080
      timeout: 30s
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      retries:
        attempts: 2
        perTryTimeout: 5s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: newtesttoday-frontend-service
          port:
            number: 80
      timeout: 15s
kind: List
metadata:
  resourceVersion: ""
