apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: dGVuYW50X25ld2N1c3RvbWVyNDAyNQ==
    DB_PASSWORD: UDdPSnZRTDloKzNWVFBrZyU=
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_SSL_VERIFY: ZmFsc2U=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"dGVuYW50X25ld2N1c3RvbWVyNDAyNQ==","DB_PASSWORD":"UDdPSnZRTDloKzNWVFBrZyU=","DB_PORT":"MzMwNg==","DB_SSL":"dHJ1ZQ==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_SSL_VERIFY":"ZmFsc2U=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"newcustomer4025-db-secret","namespace":"tenant-newcustomer4025"}}
    creationTimestamp: "2025-07-30T12:29:26Z"
    name: newcustomer4025-db-secret
    namespace: tenant-newcustomer4025
    resourceVersion: "8138985"
    uid: 0decf1a1-d571-4dec-905e-7a2ccbc03e0e
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
