#!/bin/bash

# Deploy Tenant Helm Chart Script
# This script deploys the Architrave tenant Helm chart with proper configuration

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -t, --tenant-id TENANT_ID     Tenant ID (required)"
    echo "  -d, --domain DOMAIN           Tenant domain (required)"
    echo "  -b, --bucket BUCKET           S3 bucket name (optional, auto-generated if not provided)"
    echo "  -e, --environment ENV         Environment (default: production)"
    echo "  -n, --namespace NAMESPACE     Kubernetes namespace (default: tenant-TENANT_ID)"
    echo "  -f, --values-file FILE        Additional values file (optional)"
    echo "  --dry-run                     Perform a dry run without actually deploying"
    echo "  --debug                       Enable debug output"
    echo "  -h, --help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -t acme-corp -d acme.architrave.com"
    echo "  $0 -t test-tenant -d test.architrave.com --dry-run"
    echo "  $0 -t prod-client -d client.architrave.com -e production -f custom-values.yaml"
}

# Default values
TENANT_ID=""
DOMAIN=""
BUCKET=""
ENVIRONMENT="production"
NAMESPACE=""
VALUES_FILE=""
DRY_RUN=false
DEBUG=false
CHART_PATH="./helm-charts/architrave-tenant"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--tenant-id)
            TENANT_ID="$2"
            shift 2
            ;;
        -d|--domain)
            DOMAIN="$2"
            shift 2
            ;;
        -b|--bucket)
            BUCKET="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -f|--values-file)
            VALUES_FILE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --debug)
            DEBUG=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$TENANT_ID" ]]; then
    print_error "Tenant ID is required"
    show_usage
    exit 1
fi

if [[ -z "$DOMAIN" ]]; then
    print_error "Domain is required"
    show_usage
    exit 1
fi

# Set default namespace if not provided
if [[ -z "$NAMESPACE" ]]; then
    NAMESPACE="tenant-$TENANT_ID"
fi

# Set default bucket if not provided
if [[ -z "$BUCKET" ]]; then
    BUCKET="tenant-$TENANT_ID-storage"
fi

# Validate chart exists
if [[ ! -d "$CHART_PATH" ]]; then
    print_error "Helm chart not found at: $CHART_PATH"
    exit 1
fi

print_info "Starting Helm deployment for tenant: $TENANT_ID"
print_info "Domain: $DOMAIN"
print_info "Namespace: $NAMESPACE"
print_info "S3 Bucket: $BUCKET"
print_info "Environment: $ENVIRONMENT"

# Check if Helm is installed
if ! command -v helm &> /dev/null; then
    print_error "Helm is not installed or not in PATH"
    exit 1
fi

# Check if kubectl is installed and configured
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Test kubectl connectivity
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

print_success "Prerequisites check passed"

# Create temporary values file
TEMP_VALUES=$(mktemp)
trap "rm -f $TEMP_VALUES" EXIT

cat > "$TEMP_VALUES" << EOF
tenant:
  id: "$TENANT_ID"
  domain: "$DOMAIN"

storage:
  s3:
    bucketName: "$BUCKET"

environment: "$ENVIRONMENT"
EOF

print_info "Generated tenant-specific values"

# Build Helm command
HELM_CMD="helm upgrade --install tenant-$TENANT_ID $CHART_PATH"
HELM_CMD="$HELM_CMD --namespace $NAMESPACE"
HELM_CMD="$HELM_CMD --create-namespace"
HELM_CMD="$HELM_CMD --values $TEMP_VALUES"

# Add environment-specific values file
if [[ "$ENVIRONMENT" == "production" && -f "$CHART_PATH/values-production.yaml" ]]; then
    HELM_CMD="$HELM_CMD --values $CHART_PATH/values-production.yaml"
    print_info "Using production values file"
fi

# Add custom values file if provided
if [[ -n "$VALUES_FILE" && -f "$VALUES_FILE" ]]; then
    HELM_CMD="$HELM_CMD --values $VALUES_FILE"
    print_info "Using custom values file: $VALUES_FILE"
fi

# Add common options
HELM_CMD="$HELM_CMD --wait --timeout 10m"

# Add dry-run if requested
if [[ "$DRY_RUN" == "true" ]]; then
    HELM_CMD="$HELM_CMD --dry-run"
    print_warning "Performing dry run - no actual deployment"
fi

# Add debug if requested
if [[ "$DEBUG" == "true" ]]; then
    HELM_CMD="$HELM_CMD --debug"
fi

print_info "Executing Helm command..."
if [[ "$DEBUG" == "true" ]]; then
    print_info "Command: $HELM_CMD"
fi

# Execute Helm deployment
if eval "$HELM_CMD"; then
    if [[ "$DRY_RUN" == "true" ]]; then
        print_success "Dry run completed successfully"
    else
        print_success "Helm chart deployed successfully for tenant: $TENANT_ID"
        
        # Show deployment status
        print_info "Checking deployment status..."
        kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/instance=tenant-$TENANT_ID"
        
        print_info "Checking services..."
        kubectl get services -n "$NAMESPACE" -l "app.kubernetes.io/instance=tenant-$TENANT_ID"
        
        print_success "Deployment completed! Tenant $TENANT_ID is available at: https://$DOMAIN"
    fi
else
    print_error "Helm deployment failed for tenant: $TENANT_ID"
    exit 1
fi
