#!/bin/bash

set -e

TENANT_ID="dns-test-2025"
NAMESPACE="tenant-${TENANT_ID}"
AWS_ACCOUNT_ID="************"
AWS_REGION="eu-central-1"
S3_BUCKET="architravetestdb-${TENANT_ID}"

echo "🚀 Starting comprehensive fix for ${TENANT_ID}..."

# Step 1: Get the OIDC issuer ID for the EKS cluster
echo "📋 Getting EKS cluster OIDC issuer..."
CLUSTER_NAME="production-wks"
OIDC_ISSUER=$(aws eks describe-cluster --name ${CLUSTER_NAME} --query "cluster.identity.oidc.issuer" --output text)
OIDC_ID=$(echo ${OIDC_ISSUER} | cut -d '/' -f 5)
echo "OIDC ID: ${OIDC_ID}"

# Step 2: Fix the IRSA role trust policy with correct OIDC ID
echo "🔧 Updating IRSA role trust policy..."
sed "s/YOUR_OIDC_ID/${OIDC_ID}/g" dns-test-2025-irsa-role.json > dns-test-2025-irsa-role-updated.json

# Step 3: Create/Update IAM resources
echo "🔐 Setting up IAM resources..."

# Create IRSA role
aws iam create-role \
    --role-name "${TENANT_ID}-irsa-role" \
    --assume-role-policy-document file://dns-test-2025-irsa-role-updated.json \
    --description "IRSA role for ${TENANT_ID} tenant" || echo "Role may already exist"

# Create and attach Secrets Manager policy
aws iam create-policy \
    --policy-name "${TENANT_ID}-secrets-policy" \
    --policy-document file://dns-test-2025-secrets-policy.json \
    --description "Secrets Manager access for ${TENANT_ID}" || echo "Policy may already exist"

aws iam attach-role-policy \
    --role-name "${TENANT_ID}-irsa-role" \
    --policy-arn "arn:aws:iam::${AWS_ACCOUNT_ID}:policy/${TENANT_ID}-secrets-policy"

# Step 4: Add S3 permissions to node role
echo "🪣 Adding S3 permissions to node role..."

# Create and attach S3 policy to node role
aws iam create-policy \
    --policy-name "${TENANT_ID}-s3-node-policy" \
    --policy-document file://s3-node-role-policy.json \
    --description "S3 access for ${TENANT_ID} via node role" || echo "Policy may already exist"

aws iam attach-role-policy \
    --role-name "prod-architrave-eks-node-role" \
    --policy-arn "arn:aws:iam::${AWS_ACCOUNT_ID}:policy/${TENANT_ID}-s3-node-policy"

# Step 5: Create namespace if it doesn't exist
echo "🏗️ Creating namespace..."
kubectl create namespace ${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -

# Step 6: Apply Kubernetes resources
echo "☸️ Applying Kubernetes resources..."

# Apply S3 CSI resources
kubectl apply -f dns-test-2025-s3-csi.yaml

# Apply ServiceAccount
kubectl apply -f dns-test-2025-serviceaccount.yaml

# Step 7: Verify S3 bucket exists and has content
echo "📦 Checking S3 bucket..."
if aws s3 ls "s3://${S3_BUCKET}" >/dev/null 2>&1; then
    echo "✅ S3 bucket ${S3_BUCKET} exists"
    
    # Check if public/ directory exists
    if aws s3 ls "s3://${S3_BUCKET}/public/" >/dev/null 2>&1; then
        echo "✅ public/ directory exists in bucket"
    else
        echo "⚠️ WARNING: public/ directory not found in S3 bucket"
        echo "You need to upload your web application files to s3://${S3_BUCKET}/public/"
        echo "Make sure index.php is present in the public/ directory"
    fi
else
    echo "❌ ERROR: S3 bucket ${S3_BUCKET} does not exist"
    echo "Creating S3 bucket..."
    aws s3 mb "s3://${S3_BUCKET}" --region ${AWS_REGION}
    echo "⚠️ WARNING: You need to upload your web application files to s3://${S3_BUCKET}/public/"
fi

# Step 8: Wait for PVC to be bound
echo "⏳ Waiting for PVC to be bound..."
kubectl wait --for=condition=Bound pvc/s3-pvc-${TENANT_ID} -n ${NAMESPACE} --timeout=300s || echo "PVC binding may take longer"

# Step 9: Check if deployment exists and update it
echo "🔄 Checking for existing deployment..."
if kubectl get deployment ${TENANT_ID}-webapp -n ${NAMESPACE} >/dev/null 2>&1; then
    echo "📝 Updating existing deployment to use correct ServiceAccount and mount..."
    
    # Patch deployment to use correct ServiceAccount
    kubectl patch deployment ${TENANT_ID}-webapp -n ${NAMESPACE} -p '{
        "spec": {
            "template": {
                "spec": {
                    "serviceAccountName": "'${TENANT_ID}'-aws-sa"
                }
            }
        }
    }'
    
    # Patch deployment to use correct volume mount
    kubectl patch deployment ${TENANT_ID}-webapp -n ${NAMESPACE} -p '{
        "spec": {
            "template": {
                "spec": {
                    "volumes": [
                        {
                            "name": "s3-storage",
                            "persistentVolumeClaim": {
                                "claimName": "s3-pvc-'${TENANT_ID}'"
                            }
                        }
                    ]
                }
            }
        }
    }'
    
    echo "🔄 Restarting deployment..."
    kubectl rollout restart deployment/${TENANT_ID}-webapp -n ${NAMESPACE}
    kubectl rollout status deployment/${TENANT_ID}-webapp -n ${NAMESPACE} --timeout=300s
else
    echo "⚠️ No deployment found. You may need to create the deployment first."
fi

echo "✅ Fix completed! Next steps:"
echo ""
echo "1. Verify S3 bucket content:"
echo "   aws s3 ls s3://${S3_BUCKET}/public/ --recursive"
echo ""
echo "2. If public/ directory is empty, upload your web app files:"
echo "   aws s3 sync /path/to/your/webapp/public/ s3://${S3_BUCKET}/public/"
echo ""
echo "3. Check pod status:"
echo "   kubectl get pods -n ${NAMESPACE}"
echo ""
echo "4. Check mount status:"
echo "   kubectl exec -n ${NAMESPACE} deployment/${TENANT_ID}-webapp -- ls -la /storage/clear/public"
echo ""
echo "5. For Secrets Manager, set the SECRET_ID environment variable in your deployment:"
echo "   kubectl set env deployment/${TENANT_ID}-webapp -n ${NAMESPACE} SECRET_ID=your-secret-arn"

# Cleanup temporary files
rm -f dns-test-2025-irsa-role-updated.json

echo "🎉 All fixes applied successfully!"