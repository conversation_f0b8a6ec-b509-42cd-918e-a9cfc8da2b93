apiVersion: v1
items:
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-30T12:51:16+02:00"
    creationTimestamp: "2025-07-30T17:24:30Z"
    generateName: newcustomer3025-backend-8667d5f558-
    labels:
      app: newcustomer3025-backend
      component: backend
      pod-template-hash: 8667d5f558
      tenant: newcustomer3025
    name: newcustomer3025-backend-8667d5f558-c2gc5
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newcustomer3025-backend-8667d5f558
      uid: c3e3fb4f-55af-40a5-a8c5-512d7280ca09
    resourceVersion: "8250092"
    uid: f6279a94-cd27-4522-b28e-c2977efef2bb
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: newcustomer3025
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            key: DB_HOST
            name: newcustomer3025-db-secret
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            key: DB_PORT
            name: newcustomer3025-db-secret
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            key: DB_NAME
            name: newcustomer3025-db-secret
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: newcustomer3025-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: newcustomer3025-db-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: newcustomer3025-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: newcustomer3025-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: newcustomer3025-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@newcustomer3025-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant newcustomer3025..."
        aws s3 sync s3://tenant-newcustomer3025/newcustomer3025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-11-167.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: newcustomer3025-s3-service-account
    serviceAccountName: newcustomer3025-s3-service-account
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-8xnnc
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:32Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:53Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:25:56Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:25:56Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:30Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://55816e35451206fc85143b8839c8b1af7f98685651b659adc14f4a273e15bc12
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T17:24:54Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://f5b7137a82faf0227e2fcec559ef924484d7a2e4b04f139294035f411654c034
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T17:24:54Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://b0c88bf3d5da79cb975f4e1e8ef9041bb4c2be765fd2d7649843e0e555464938
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:a5b482028826a2e109c53eb70c64ca70973c06265347103c7385f115aea772c7
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://b0c88bf3d5da79cb975f4e1e8ef9041bb4c2be765fd2d7649843e0e555464938
          exitCode: 0
          finishedAt: "2025-07-30T17:24:35Z"
          reason: Completed
          startedAt: "2025-07-30T17:24:32Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://bf6abbc60eb2a520bfc23af937b556dd07591a8815e7b92150d3c3443e1767c2
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://bf6abbc60eb2a520bfc23af937b556dd07591a8815e7b92150d3c3443e1767c2
          exitCode: 0
          finishedAt: "2025-07-30T17:24:38Z"
          reason: Completed
          startedAt: "2025-07-30T17:24:37Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://40ebe9295c2f067af310c72d046d138995bd35624674e7f23e4bce304007d249
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://40ebe9295c2f067af310c72d046d138995bd35624674e7f23e4bce304007d249
          exitCode: 0
          finishedAt: "2025-07-30T17:24:49Z"
          reason: Completed
          startedAt: "2025-07-30T17:24:40Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://708b622f4d6df143849e2a2b632422e2b57264c4feb1f8d22a4404c79bae11e1
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://708b622f4d6df143849e2a2b632422e2b57264c4feb1f8d22a4404c79bae11e1
          exitCode: 0
          finishedAt: "2025-07-30T17:24:51Z"
          reason: Completed
          startedAt: "2025-07-30T17:24:50Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://57fbd7c60f97c208662027a1a749d97496c886f143b364c8c22f5d78a87cb487
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://57fbd7c60f97c208662027a1a749d97496c886f143b364c8c22f5d78a87cb487
          exitCode: 0
          finishedAt: "2025-07-30T17:24:52Z"
          reason: Completed
          startedAt: "2025-07-30T17:24:52Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://dc13dc64e5b8412320b27e53fa11767b5c042f1312fdbe157870a8bb1b471ef0
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://dc13dc64e5b8412320b27e53fa11767b5c042f1312fdbe157870a8bb1b471ef0
          exitCode: 0
          finishedAt: "2025-07-30T17:24:53Z"
          reason: Completed
          startedAt: "2025-07-30T17:24:53Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-8xnnc
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-30T17:24:30Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-30T12:51:16+02:00"
    creationTimestamp: "2025-07-30T10:54:09Z"
    generateName: newcustomer3025-backend-8667d5f558-
    labels:
      app: newcustomer3025-backend
      component: backend
      pod-template-hash: 8667d5f558
      tenant: newcustomer3025
    name: newcustomer3025-backend-8667d5f558-dq5cb
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newcustomer3025-backend-8667d5f558
      uid: c3e3fb4f-55af-40a5-a8c5-512d7280ca09
    resourceVersion: "8104515"
    uid: eff1a41d-b6b4-4780-b299-30c8a0e75ecd
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: newcustomer3025
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            key: DB_HOST
            name: newcustomer3025-db-secret
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            key: DB_PORT
            name: newcustomer3025-db-secret
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            key: DB_NAME
            name: newcustomer3025-db-secret
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: newcustomer3025-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: newcustomer3025-db-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: newcustomer3025-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: newcustomer3025-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: newcustomer3025-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@newcustomer3025-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant newcustomer3025..."
        aws s3 sync s3://tenant-newcustomer3025/newcustomer3025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-newcustomer3025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-10-99.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: newcustomer3025-s3-service-account
    serviceAccountName: newcustomer3025-s3-service-account
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-7mbcl
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T10:54:11Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T10:54:24Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T10:55:27Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T10:55:27Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T10:54:09Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://59949b1ded6cc6ad08efa1bc67a47d509ec3e48da7d25b5008a7f3fde07b0080
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T10:54:24Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://cd096bcb312b595e963bbae5edc512f3e5b59e46ccc1905d742b2536e54a1e7e
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T10:54:24Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: **********
    hostIPs:
    - ip: **********
    initContainerStatuses:
    - containerID: containerd://180347d8a636468c73828f727d34f1a94244b353fa370bef20cc8153d81241ca
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:a5b482028826a2e109c53eb70c64ca70973c06265347103c7385f115aea772c7
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://180347d8a636468c73828f727d34f1a94244b353fa370bef20cc8153d81241ca
          exitCode: 0
          finishedAt: "2025-07-30T10:54:12Z"
          reason: Completed
          startedAt: "2025-07-30T10:54:10Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://6560c40a48f267d16dcf778722a1b4a496dc8648bb83837f61cfc69569927dba
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://6560c40a48f267d16dcf778722a1b4a496dc8648bb83837f61cfc69569927dba
          exitCode: 0
          finishedAt: "2025-07-30T10:54:14Z"
          reason: Completed
          startedAt: "2025-07-30T10:54:14Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://f0d5ffb7f233c98b559965d9bad1b2f8290a2083e6bfa19449daeafdc49379bc
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://f0d5ffb7f233c98b559965d9bad1b2f8290a2083e6bfa19449daeafdc49379bc
          exitCode: 0
          finishedAt: "2025-07-30T10:54:19Z"
          reason: Completed
          startedAt: "2025-07-30T10:54:15Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://6018bc042cf7be5428749c19de7b4d98f22bff56f5d10b90a16005b4d23d70b9
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://6018bc042cf7be5428749c19de7b4d98f22bff56f5d10b90a16005b4d23d70b9
          exitCode: 0
          finishedAt: "2025-07-30T10:54:21Z"
          reason: Completed
          startedAt: "2025-07-30T10:54:21Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://75bcf104c280e79c4c15515595412c0fb15cdf4e228b397096dd919b23a317c0
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://75bcf104c280e79c4c15515595412c0fb15cdf4e228b397096dd919b23a317c0
          exitCode: 0
          finishedAt: "2025-07-30T10:54:22Z"
          reason: Completed
          startedAt: "2025-07-30T10:54:22Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://3b7aca2e8ec11ee2322d972e5a0abe1c6d79d2a831c7f279b23561ae0985a03c
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://3b7aca2e8ec11ee2322d972e5a0abe1c6d79d2a831c7f279b23561ae0985a03c
          exitCode: 0
          finishedAt: "2025-07-30T10:54:24Z"
          reason: Completed
          startedAt: "2025-07-30T10:54:24Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-7mbcl
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: **********
    podIPs:
    - ip: **********
    qosClass: Burstable
    startTime: "2025-07-30T10:54:09Z"
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-30T17:39:39Z"
    generateName: newcustomer3025-frontend-5467bc8c55-
    labels:
      app: newcustomer3025-frontend
      component: frontend
      pod-template-hash: 5467bc8c55
      tenant: newcustomer3025
    name: newcustomer3025-frontend-5467bc8c55-njwxj
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newcustomer3025-frontend-5467bc8c55
      uid: 61c1059b-836b-4839-b479-e2acf151a579
    resourceVersion: "8255456"
    uid: 01b9d49d-a58b-4c0a-a084-c8d7587496f5
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: newcustomer3025
      - name: DOMAIN
        value: architrave-assets.de
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: BACKEND_URL
        value: http://newcustomer3025-backend-service:8080
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        httpGet:
          path: /health
          port: 80
          scheme: HTTP
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 1
      name: frontend
      ports:
      - containerPort: 80
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        httpGet:
          path: /health
          port: 80
          scheme: HTTP
        initialDelaySeconds: 10
        periodSeconds: 10
        successThreshold: 1
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hsqb6
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for tenant newcustomer3025..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 80;
            server_name newcustomer3025.architrave-assets.de localhost;
            root /usr/share/nginx/html;
            index index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # API routes - proxy to backend service
            location /api/ {
                proxy_pass http://newcustomer3025-backend-service:8080/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # Frontend routes - serve static files
            location / {
                try_files $uri $uri/ /index.html;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }

            # Static assets with caching
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1h;
                add_header Cache-Control "public, immutable";
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully for tenant newcustomer3025"
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hsqb6
        readOnly: true
    nodeName: ip-10-0-12-210.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-hsqb6
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:39:42Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:39:42Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:40:00Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:40:00Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:39:39Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://78e69eb911de9cf11087b19957231c28df1f76bd46a914e27dfb877d7bf95ea5
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:42618102e5cc405c551c142acd857aaaa953559b63ca02fff46760e34d4ff149
      lastState: {}
      name: frontend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T17:39:48Z"
      volumeMounts:
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hsqb6
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://7ed18147752acc420efb3b95e45c363e30193ec1e7de8e3528a5624f4c7b80a8
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://7ed18147752acc420efb3b95e45c363e30193ec1e7de8e3528a5624f4c7b80a8
          exitCode: 0
          finishedAt: "2025-07-30T17:39:42Z"
          reason: Completed
          startedAt: "2025-07-30T17:39:42Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hsqb6
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-30T17:39:39Z"
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-30T17:24:30Z"
    generateName: newcustomer3025-rabbitmq-7678fcb67d-
    labels:
      app: newcustomer3025-rabbitmq
      component: rabbitmq
      pod-template-hash: 7678fcb67d
      tenant: newcustomer3025
    name: newcustomer3025-rabbitmq-7678fcb67d-l46vr
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newcustomer3025-rabbitmq-7678fcb67d
      uid: bcae00cf-5299-4284-acac-d3100984f7ae
    resourceVersion: "8249212"
    uid: a984b3f1-29b4-4e12-989b-d014ccd7272a
  spec:
    containers:
    - env:
      - name: RABBITMQ_DEFAULT_USER
        value: guest
      - name: RABBITMQ_DEFAULT_PASS
        value: guest
      - name: TENANT_ID
        value: newcustomer3025
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imagePullPolicy: IfNotPresent
      name: rabbitmq
      ports:
      - containerPort: 80
        protocol: TCP
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-fzwhc
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-10-209.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-fzwhc
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:37Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:30Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:37Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:37Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T17:24:30Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://23f84197d0e2f4e12d31b78683e8eecea0892da2040ec08ec6d7947a5a207c3f
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev@sha256:a40d91656430a34f8ee2dfbac222d974915ef9d448f7e99ecb4118218a8a84c0
      lastState: {}
      name: rabbitmq
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T17:24:37Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-fzwhc
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: BestEffort
    startTime: "2025-07-30T17:24:30Z"
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-backend","component":"backend","tenant":"newcustomer3025"},"name":"newcustomer3025-backend-service","namespace":"tenant-newcustomer3025"},"spec":{"ports":[{"name":"http","port":8080,"protocol":"TCP","targetPort":8080}],"selector":{"app":"newcustomer3025-backend","component":"backend","tenant":"newcustomer3025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T10:51:11Z"
    labels:
      app: newcustomer3025-backend
      component: backend
      tenant: newcustomer3025
    name: newcustomer3025-backend-service
    namespace: tenant-newcustomer3025
    resourceVersion: "8102624"
    uid: 913dc6a1-6979-4666-959f-7669eabb9614
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
    selector:
      app: newcustomer3025-backend
      component: backend
      tenant: newcustomer3025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-frontend","component":"frontend","tenant":"newcustomer3025"},"name":"newcustomer3025-frontend-service","namespace":"tenant-newcustomer3025"},"spec":{"ports":[{"port":80,"protocol":"TCP","targetPort":80}],"selector":{"app":"newcustomer3025-frontend","component":"frontend","tenant":"newcustomer3025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T10:51:19Z"
    labels:
      app: newcustomer3025-frontend
      component: frontend
      tenant: newcustomer3025
    name: newcustomer3025-frontend-service
    namespace: tenant-newcustomer3025
    resourceVersion: "8102727"
    uid: 5547b734-c84c-495b-8f2f-0bc20b9f1a12
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 80
      protocol: TCP
      targetPort: 80
    selector:
      app: newcustomer3025-frontend
      component: frontend
      tenant: newcustomer3025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"newcustomer3025"},"name":"newcustomer3025-rabbitmq-mgmt-service","namespace":"tenant-newcustomer3025"},"spec":{"ports":[{"port":15672,"protocol":"TCP","targetPort":15672}],"selector":{"app":"newcustomer3025-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"newcustomer3025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T10:48:18Z"
    labels:
      app: newcustomer3025-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: newcustomer3025
    name: newcustomer3025-rabbitmq-mgmt-service
    namespace: tenant-newcustomer3025
    resourceVersion: "8102747"
    uid: dac44dfe-76b1-412c-85ce-e22029240acf
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 15672
      protocol: TCP
      targetPort: 15672
    selector:
      app: newcustomer3025-rabbitmq
      component: rabbitmq
      tenant: newcustomer3025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-rabbitmq","component":"rabbitmq","tenant":"newcustomer3025"},"name":"newcustomer3025-rabbitmq-service","namespace":"tenant-newcustomer3025"},"spec":{"ports":[{"port":5672,"protocol":"TCP","targetPort":5672}],"selector":{"app":"newcustomer3025-rabbitmq","component":"rabbitmq","tenant":"newcustomer3025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T10:48:17Z"
    labels:
      app: newcustomer3025-rabbitmq
      component: rabbitmq
      tenant: newcustomer3025
    name: newcustomer3025-rabbitmq-service
    namespace: tenant-newcustomer3025
    resourceVersion: "8101468"
    uid: b5880b1c-d4da-424b-92f6-25d887879871
  spec:
    clusterIP: **************
    clusterIPs:
    - **************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 5672
      protocol: TCP
      targetPort: 5672
    selector:
      app: newcustomer3025-rabbitmq
      component: rabbitmq
      tenant: newcustomer3025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "4"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-backend","component":"backend","tenant":"newcustomer3025"},"name":"newcustomer3025-backend","namespace":"tenant-newcustomer3025"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"newcustomer3025-backend","component":"backend","tenant":"newcustomer3025"}},"template":{"metadata":{"labels":{"app":"newcustomer3025-backend","component":"backend","tenant":"newcustomer3025"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"newcustomer3025"},{"name":"DB_HOST","valueFrom":{"secretKeyRef":{"key":"DB_HOST","name":"newcustomer3025-db-secret"}}},{"name":"DB_PORT","valueFrom":{"secretKeyRef":{"key":"DB_PORT","name":"newcustomer3025-db-secret"}}},{"name":"DB_NAME","valueFrom":{"secretKeyRef":{"key":"DB_NAME","name":"newcustomer3025-db-secret"}}},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"DB_USER","name":"newcustomer3025-db-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"DB_PASSWORD","name":"newcustomer3025-db-secret"}}},{"name":"DB_SSL","valueFrom":{"secretKeyRef":{"key":"DB_SSL","name":"newcustomer3025-db-secret"}}},{"name":"DB_SSL_CA","valueFrom":{"secretKeyRef":{"key":"DB_SSL_CA","name":"newcustomer3025-db-secret"}}},{"name":"DB_SSL_VERIFY","valueFrom":{"secretKeyRef":{"key":"DB_SSL_VERIFY","name":"newcustomer3025-db-secret"}}},{"name":"RABBITMQ_URL","value":"amqp://guest:guest@newcustomer3025-rabbitmq-service:5672/"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test","livenessProbe":{"initialDelaySeconds":60,"periodSeconds":30,"tcpSocket":{"port":9000}},"name":"backend","ports":[{"containerPort":9000}],"readinessProbe":{"initialDelaySeconds":30,"periodSeconds":30,"tcpSocket":{"port":9000}},"resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}},"volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"},{"mountPath":"/shared-app","name":"shared-app"}]},{"image":"nginx:1.21-alpine","livenessProbe":{"failureThreshold":5,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-liveness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":90,"periodSeconds":60,"successThreshold":1,"timeoutSeconds":10},"name":"nginx","ports":[{"containerPort":8080}],"readinessProbe":{"failureThreshold":10,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-readiness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"timeoutSeconds":5},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"},{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Downloading RDS CA certificate...\"\ncurl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem\necho \"SSL certificate downloaded successfully\"\n"],"image":"curlimages/curl:latest","name":"ssl-cert-downloader","volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"}]},{"command":["sh","-c","echo \"Copying application files from /storage/ArchAssets to shared volume...\"\nif [ -d \"/storage/ArchAssets\" ]; then\n    cp -r /storage/ArchAssets/* /shared-app/ 2\u003e/dev/null || true\n    echo \"Application files copied successfully from /storage/ArchAssets\"\n    echo \"Copied files:\"\n    ls -la /shared-app/ | head -10\nelse\n    echo \"Warning: /storage/ArchAssets directory not found in webapp_dev image\"\n    echo \"Available directories in /storage:\"\n    ls -la /storage/ 2\u003e/dev/null || echo \"No /storage directory\"\n    echo \"Available directories in root:\"\n    ls -la / | grep -E \"(var|storage|app)\" || echo \"No relevant directories found\"\nfi\necho \"Application files copy process completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test","name":"app-files-copier","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Fixing PHP application config path issues and PHP-FPM compatibility...\"\n\n# Fix the relative path issue in /shared-app/public/api/index.php\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Found API index.php, fixing config path...\"\n\n  # Create backup\n  cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup\n\n  # Fix the relative path issue by using absolute path\n  sed -i \"s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g\" /shared-app/public/api/index.php\n\n  echo \"Config path fixed in API index.php\"\n  echo \"Checking the fix:\"\n  grep -n \"require.*config.*application.config.php\" /shared-app/public/api/index.php || echo \"Pattern not found after fix\"\nelse\n  echo \"Warning: /shared-app/public/api/index.php not found\"\n  echo \"Available files in /shared-app/public/:\"\n  ls -la /shared-app/public/ 2\u003e/dev/null || echo \"No public directory\"\nfi\n\n# Create PHP-FPM compatibility polyfill for apache_request_headers()\necho \"Creating PHP-FPM compatibility polyfill...\"\ncat \u003e /shared-app/php_fpm_polyfill.php \u003c\u003c 'POLYFILL_EOF'\n\u003c?php\n/**\n * PHP-FPM Compatibility Polyfill for Apache functions\n * This file provides missing Apache functions for PHP-FPM + nginx environments\n *\n * NOTE: Using forced function definition without conditional checks\n * because function_exists() may return false positives in some PHP-FPM environments\n */\n\n/**\n * Polyfill for apache_request_headers() function in PHP-FPM environment\n * Only define if the function doesn't already exist\n */\nif (!function_exists('apache_request_headers')) {\n    function apache_request_headers() {\n        $headers = array();\n\n        // Get all HTTP headers from $_SERVER superglobal\n        foreach ($_SERVER as $key =\u003e $value) {\n            if (strpos($key, 'HTTP_') === 0) {\n                // Convert HTTP_HEADER_NAME to Header-Name format\n                $header = str_replace('_', '-', substr($key, 5));\n                $header = ucwords(strtolower($header), '-');\n                $headers[$header] = $value;\n            }\n        }\n\n        // Add special headers that might not have HTTP_ prefix\n        if (isset($_SERVER['CONTENT_TYPE'])) {\n            $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];\n        }\n        if (isset($_SERVER['CONTENT_LENGTH'])) {\n            $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];\n        }\n\n        return $headers;\n    }\n}\n\n/**\n * Polyfill for apache_response_headers() function in PHP-FPM environment\n * @return array\n */\nfunction apache_response_headers() {\n    $headers = array();\n\n    // Get headers that were set with header() function\n    if (function_exists('headers_list')) {\n        foreach (headers_list() as $header) {\n            $parts = explode(':', $header, 2);\n            if (count($parts) === 2) {\n                $headers[trim($parts[0])] = trim($parts[1]);\n            }\n        }\n    }\n\n    return $headers;\n}\n\n/**\n * Alias for apache_request_headers() - commonly used alternative\n * Only define if the function doesn't already exist\n */\nif (!function_exists('getallheaders')) {\n    function getallheaders() {\n        return apache_request_headers();\n    }\n}\nPOLYFILL_EOF\n\necho \"✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php\"\n\n# Inject the polyfill into the main bootstrap file\nif [ -f \"/shared-app/bootstrap.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into bootstrap.php...\"\n\n  # Create backup\n  cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bootstrap.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into bootstrap.php\"\nelse\n  echo \"Warning: /shared-app/bootstrap.php not found\"\nfi\n\n# Also inject into the API index.php as a fallback\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into API index.php as fallback...\"\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/public/api/index.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into API index.php\"\nfi\n\n# CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)...\"\n\n  # Create backup\n  cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/architrave.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into CLI entry point\"\nelse\n  echo \"Warning: /shared-app/bin/architrave.php not found\"\nfi\n\n# Also check for alternative CLI entry points\nif [ -f \"/shared-app/bin/console.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into console.php...\"\n\n  # Create backup\n  cp /shared-app/bin/console.php /shared-app/bin/console.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/console.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into console.php\"\nfi\n\n# Verify config file exists\nif [ -f \"/shared-app/config/application.config.php\" ]; then\n  echo \"✅ Config file exists at /shared-app/config/application.config.php\"\nelse\n  echo \"❌ Config file missing at /shared-app/config/application.config.php\"\n  echo \"Available files in /shared-app/config/:\"\n  ls -la /shared-app/config/ 2\u003e/dev/null || echo \"No config directory\"\nfi\n\necho \"✅ PHP config path fix and PHP-FPM compatibility setup completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test","name":"php-config-fixer","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Validating PHP environment for PHP-FPM + nginx compatibility...\"\n\n# Test if polyfill was loaded correctly\nphp -r \"\nrequire_once '/shared-app/php_fpm_polyfill.php';\n\necho 'Testing PHP-FPM polyfill functions...\\n';\n\n// Test apache_request_headers function\nif (function_exists('apache_request_headers')) {\n    echo '✅ apache_request_headers() function is available\\n';\n} else {\n    echo '❌ apache_request_headers() function is missing\\n';\n    exit(1);\n}\n\n// Test getallheaders function\nif (function_exists('getallheaders')) {\n    echo '✅ getallheaders() function is available\\n';\n} else {\n    echo '❌ getallheaders() function is missing\\n';\n    exit(1);\n}\n\n// Test apache_response_headers function\nif (function_exists('apache_response_headers')) {\n    echo '✅ apache_response_headers() function is available\\n';\n} else {\n    echo '❌ apache_response_headers() function is missing\\n';\n    exit(1);\n}\n\necho '✅ All PHP-FPM polyfill functions are working correctly\\n';\n\"\n\n# Test basic PHP application loading\necho \"Testing PHP application bootstrap loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    echo '✅ Bootstrap loaded successfully\\n';\n} catch (Exception \\$e) {\n    echo '❌ Bootstrap loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# Test Laminas application config loading\necho \"Testing Laminas application config loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    \\$config = require '/shared-app/config/application.config.php';\n    echo '✅ Application config loaded successfully\\n';\n    echo 'Config modules: ' . count(\\$config['modules']) . '\\n';\n} catch (Exception \\$e) {\n    echo '❌ Application config loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# CRITICAL FIX: Test CLI entry point functionality\necho \"Testing CLI entry point functionality...\"\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Testing bin/architrave.php polyfill integration...\"\n  php -d display_errors=1 -d error_reporting=E_ALL -r \"\n  try {\n      // Change to the correct directory\n      chdir('/shared-app');\n\n      // Test if polyfill file exists and is readable\n      if (!file_exists('php_fpm_polyfill.php')) {\n          echo '❌ Polyfill file not found\\n';\n          exit(1);\n      }\n\n      // Load polyfill manually to test\n      require_once 'php_fpm_polyfill.php';\n\n      // Test if functions are available\n      if (function_exists('apache_request_headers')) {\n          echo '✅ CLI polyfill functions available\\n';\n      } else {\n          echo '❌ CLI polyfill functions missing\\n';\n          exit(1);\n      }\n\n      echo '✅ CLI environment validation successful\\n';\n  } catch (Exception \\$e) {\n      echo '⚠️ CLI test failed: ' . \\$e-\u003egetMessage() . '\\n';\n      echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n      echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n      // Don't exit with error as this might fail in init container context\n  }\n  \" || echo \"⚠️ CLI test completed with warnings (normal in init container)\"\nelse\n  echo \"⚠️ CLI entry point not found - will be available after application sync\"\nfi\n\necho \"✅ PHP environment validation completed successfully\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test","name":"php-env-validator","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Creating HTTP nginx configuration for port 8080...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 8080;\n    server_name localhost;\n    root /shared-app/public;\n    index index.php index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /api/health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # Root endpoint - redirect to API\n    location = / {\n        return 302 /api/;\n    }\n\n    location / {\n        try_files $uri $uri/ /api/index.php?$args;\n    }\n\n    location ~ \\.php$ {\n        fastcgi_pass localhost:9000;\n        fastcgi_index index.php;\n        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\n        fastcgi_param AUTOMATED_TEST 1;\n        include fastcgi_params;\n        fastcgi_read_timeout 310;\n    }\n\n    location ~ /\\.ht {\n        deny all;\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"ssl-certs"},{"emptyDir":{},"name":"shared-app"},{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-30T10:48:19Z"
    generation: 5
    labels:
      app: newcustomer3025-backend
      component: backend
      tenant: newcustomer3025
    name: newcustomer3025-backend
    namespace: tenant-newcustomer3025
    resourceVersion: "8250097"
    uid: 842a8580-9945-4b44-92a3-44aee49860bc
  spec:
    progressDeadlineSeconds: 600
    replicas: 2
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: newcustomer3025-backend
        component: backend
        tenant: newcustomer3025
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:51:16+02:00"
        creationTimestamp: null
        labels:
          app: newcustomer3025-backend
          component: backend
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: newcustomer3025
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newcustomer3025-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newcustomer3025-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newcustomer3025-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newcustomer3025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newcustomer3025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newcustomer3025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newcustomer3025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newcustomer3025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newcustomer3025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant newcustomer3025..."
            aws s3 sync s3://tenant-newcustomer3025/newcustomer3025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: newcustomer3025-s3-service-account
        serviceAccountName: newcustomer3025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    conditions:
    - lastTransitionTime: "2025-07-30T10:48:19Z"
      lastUpdateTime: "2025-07-30T10:52:48Z"
      message: ReplicaSet "newcustomer3025-backend-8667d5f558" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-30T17:25:56Z"
      lastUpdateTime: "2025-07-30T17:25:56Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 5
    readyReplicas: 2
    replicas: 2
    updatedReplicas: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-frontend","component":"frontend","tenant":"newcustomer3025"},"name":"newcustomer3025-frontend","namespace":"tenant-newcustomer3025"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"newcustomer3025-frontend","component":"frontend","tenant":"newcustomer3025"}},"template":{"metadata":{"labels":{"app":"newcustomer3025-frontend","component":"frontend","tenant":"newcustomer3025"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"newcustomer3025"},{"name":"DOMAIN","value":"architrave-assets.de"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"},{"name":"BACKEND_URL","value":"http://newcustomer3025-backend-service:8080"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl","livenessProbe":{"httpGet":{"path":"/health","port":80},"initialDelaySeconds":30,"periodSeconds":30},"name":"frontend","ports":[{"containerPort":80}],"readinessProbe":{"httpGet":{"path":"/health","port":80},"initialDelaySeconds":10,"periodSeconds":10},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Creating HTTP nginx configuration for tenant newcustomer3025...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 80;\n    server_name newcustomer3025.architrave-assets.de localhost;\n    root /usr/share/nginx/html;\n    index index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # API routes - proxy to backend service\n    location /api/ {\n        proxy_pass http://newcustomer3025-backend-service:8080/api/;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n\n    # Frontend routes - serve static files\n    location / {\n        try_files $uri $uri/ /index.html;\n        add_header Cache-Control \"no-cache, no-store, must-revalidate\";\n        add_header Pragma \"no-cache\";\n        add_header Expires \"0\";\n    }\n\n    # Static assets with caching\n    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully for tenant newcustomer3025\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-30T10:51:18Z"
    generation: 1
    labels:
      app: newcustomer3025-frontend
      component: frontend
      tenant: newcustomer3025
    name: newcustomer3025-frontend
    namespace: tenant-newcustomer3025
    resourceVersion: "8255462"
    uid: 5b070f78-01f7-425e-8659-87d34caca97d
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: newcustomer3025-frontend
        component: frontend
        tenant: newcustomer3025
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newcustomer3025-frontend
          component: frontend
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newcustomer3025
          - name: DOMAIN
            value: architrave-assets.de
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://newcustomer3025-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant newcustomer3025..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name newcustomer3025.architrave-assets.de localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://newcustomer3025-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant newcustomer3025"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-30T10:51:18Z"
      lastUpdateTime: "2025-07-30T10:51:31Z"
      message: ReplicaSet "newcustomer3025-frontend-5467bc8c55" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-30T17:40:01Z"
      lastUpdateTime: "2025-07-30T17:40:01Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-rabbitmq","component":"rabbitmq","tenant":"newcustomer3025"},"name":"newcustomer3025-rabbitmq","namespace":"tenant-newcustomer3025"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"newcustomer3025-rabbitmq","component":"rabbitmq","tenant":"newcustomer3025"}},"template":{"metadata":{"labels":{"app":"newcustomer3025-rabbitmq","component":"rabbitmq","tenant":"newcustomer3025"}},"spec":{"containers":[{"env":[{"name":"RABBITMQ_DEFAULT_USER","value":"guest"},{"name":"RABBITMQ_DEFAULT_PASS","value":"guest"},{"name":"TENANT_ID","value":"newcustomer3025"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02","name":"rabbitmq","ports":[{"containerPort":80}]}]}}}}
    creationTimestamp: "2025-07-30T10:48:16Z"
    generation: 1
    labels:
      app: newcustomer3025-rabbitmq
      component: rabbitmq
      tenant: newcustomer3025
    name: newcustomer3025-rabbitmq
    namespace: tenant-newcustomer3025
    resourceVersion: "8249224"
    uid: 0fe0914e-4772-4d2b-b8fe-ee8a2a697cfa
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: newcustomer3025-rabbitmq
        component: rabbitmq
        tenant: newcustomer3025
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newcustomer3025-rabbitmq
          component: rabbitmq
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: newcustomer3025
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-30T10:48:16Z"
      lastUpdateTime: "2025-07-30T10:48:16Z"
      message: ReplicaSet "newcustomer3025-rabbitmq-7678fcb67d" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-30T17:24:37Z"
      lastUpdateTime: "2025-07-30T17:24:37Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-30T10:48:19Z"
    generation: 2
    labels:
      app: newcustomer3025-backend
      component: backend
      pod-template-hash: 5cbff8f6bc
      tenant: newcustomer3025
    name: newcustomer3025-backend-5cbff8f6bc
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newcustomer3025-backend
      uid: 842a8580-9945-4b44-92a3-44aee49860bc
    resourceVersion: "8102649"
    uid: acfb2658-ea84-4594-a222-d4436b93ac46
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newcustomer3025-backend
        component: backend
        pod-template-hash: 5cbff8f6bc
        tenant: newcustomer3025
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newcustomer3025-backend
          component: backend
          pod-template-hash: 5cbff8f6bc
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newcustomer3025
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newcustomer3025-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newcustomer3025-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newcustomer3025-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newcustomer3025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newcustomer3025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newcustomer3025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newcustomer3025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newcustomer3025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newcustomer3025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "3"
    creationTimestamp: "2025-07-30T10:51:17Z"
    generation: 2
    labels:
      app: newcustomer3025-backend
      component: backend
      pod-template-hash: 664f95f56d
      tenant: newcustomer3025
    name: newcustomer3025-backend-664f95f56d
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newcustomer3025-backend
      uid: 842a8580-9945-4b44-92a3-44aee49860bc
    resourceVersion: "8102872"
    uid: e790d9cd-c6f3-4cc9-af32-cb6783472c94
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newcustomer3025-backend
        component: backend
        pod-template-hash: 664f95f56d
        tenant: newcustomer3025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:51:16+02:00"
        creationTimestamp: null
        labels:
          app: newcustomer3025-backend
          component: backend
          pod-template-hash: 664f95f56d
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newcustomer3025
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newcustomer3025-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newcustomer3025-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newcustomer3025-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newcustomer3025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newcustomer3025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newcustomer3025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newcustomer3025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newcustomer3025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newcustomer3025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "4"
    creationTimestamp: "2025-07-30T10:51:31Z"
    generation: 3
    labels:
      app: newcustomer3025-backend
      component: backend
      pod-template-hash: 8667d5f558
      tenant: newcustomer3025
    name: newcustomer3025-backend-8667d5f558
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newcustomer3025-backend
      uid: 842a8580-9945-4b44-92a3-44aee49860bc
    resourceVersion: "8250095"
    uid: c3e3fb4f-55af-40a5-a8c5-512d7280ca09
  spec:
    replicas: 2
    selector:
      matchLabels:
        app: newcustomer3025-backend
        component: backend
        pod-template-hash: 8667d5f558
        tenant: newcustomer3025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:51:16+02:00"
        creationTimestamp: null
        labels:
          app: newcustomer3025-backend
          component: backend
          pod-template-hash: 8667d5f558
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: newcustomer3025
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newcustomer3025-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newcustomer3025-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newcustomer3025-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newcustomer3025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newcustomer3025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newcustomer3025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newcustomer3025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newcustomer3025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newcustomer3025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant newcustomer3025..."
            aws s3 sync s3://tenant-newcustomer3025/newcustomer3025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: newcustomer3025-s3-service-account
        serviceAccountName: newcustomer3025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    fullyLabeledReplicas: 2
    observedGeneration: 3
    readyReplicas: 2
    replicas: 2
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-30T10:50:00Z"
    generation: 2
    labels:
      app: newcustomer3025-backend
      component: backend
      pod-template-hash: 8dd68b94b
      tenant: newcustomer3025
    name: newcustomer3025-backend-8dd68b94b
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newcustomer3025-backend
      uid: 842a8580-9945-4b44-92a3-44aee49860bc
    resourceVersion: "8103419"
    uid: 506eaedf-d295-43e1-ae65-db05763e3b90
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: newcustomer3025-backend
        component: backend
        pod-template-hash: 8dd68b94b
        tenant: newcustomer3025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-30T12:50:00+02:00"
        creationTimestamp: null
        labels:
          app: newcustomer3025-backend
          component: backend
          pod-template-hash: 8dd68b94b
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newcustomer3025
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newcustomer3025-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newcustomer3025-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newcustomer3025-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newcustomer3025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newcustomer3025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newcustomer3025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newcustomer3025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newcustomer3025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@newcustomer3025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.58-test
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-30T10:51:18Z"
    generation: 1
    labels:
      app: newcustomer3025-frontend
      component: frontend
      pod-template-hash: 5467bc8c55
      tenant: newcustomer3025
    name: newcustomer3025-frontend-5467bc8c55
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newcustomer3025-frontend
      uid: 5b070f78-01f7-425e-8659-87d34caca97d
    resourceVersion: "8255458"
    uid: 61c1059b-836b-4839-b479-e2acf151a579
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: newcustomer3025-frontend
        component: frontend
        pod-template-hash: 5467bc8c55
        tenant: newcustomer3025
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newcustomer3025-frontend
          component: frontend
          pod-template-hash: 5467bc8c55
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newcustomer3025
          - name: DOMAIN
            value: architrave-assets.de
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://newcustomer3025-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant newcustomer3025..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name newcustomer3025.architrave-assets.de localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://newcustomer3025-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant newcustomer3025"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-30T10:48:16Z"
    generation: 1
    labels:
      app: newcustomer3025-rabbitmq
      component: rabbitmq
      pod-template-hash: 7678fcb67d
      tenant: newcustomer3025
    name: newcustomer3025-rabbitmq-7678fcb67d
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newcustomer3025-rabbitmq
      uid: 0fe0914e-4772-4d2b-b8fe-ee8a2a697cfa
    resourceVersion: "8249216"
    uid: bcae00cf-5299-4284-acac-d3100984f7ae
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: newcustomer3025-rabbitmq
        component: rabbitmq
        pod-template-hash: 7678fcb67d
        tenant: newcustomer3025
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newcustomer3025-rabbitmq
          component: rabbitmq
          pod-template-hash: 7678fcb67d
          tenant: newcustomer3025
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: newcustomer3025
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"keda.sh/v1alpha1","kind":"ScaledObject","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-backend","tenant":"newcustomer3025"},"name":"newcustomer3025-backend-scaler","namespace":"tenant-newcustomer3025"},"spec":{"advanced":{"horizontalPodAutoscalerConfig":{"behavior":{"scaleDown":{"policies":[{"periodSeconds":60,"type":"Percent","value":50}],"stabilizationWindowSeconds":300},"scaleUp":{"policies":[{"periodSeconds":30,"type":"Percent","value":100},{"periodSeconds":30,"type":"Pods","value":2}],"selectPolicy":"Max","stabilizationWindowSeconds":0}}}},"cooldownPeriod":300,"maxReplicaCount":20,"minReplicaCount":2,"pollingInterval":15,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"newcustomer3025-backend"},"triggers":[{"metadata":{"value":"70"},"metricType":"Utilization","type":"cpu"},{"metadata":{"value":"80"},"metricType":"Utilization","type":"memory"},{"metadata":{"metricName":"http_requests_per_second","query":"sum(rate(istio_requests_total{destination_service_name=\"newcustomer3025-backend\"}[2m]))","serverAddress":"http://prometheus-server.monitoring.svc.cluster.local:9090","threshold":"50"},"type":"prometheus"},{"metadata":{"host":"newcustomer3025-rabbitmq.tenant-newcustomer3025.svc.cluster.local","mode":"QueueLength","password":"guest","port":"15672","protocol":"http","queueName":"tenant-queue","username":"guest","value":"5"},"type":"rabbitmq"}]}}
    creationTimestamp: "2025-07-30T10:53:54Z"
    labels:
      app: newcustomer3025-backend
      app.kubernetes.io/managed-by: keda-operator
      app.kubernetes.io/name: keda-hpa-newcustomer3025-backend-scaler
      app.kubernetes.io/part-of: newcustomer3025-backend-scaler
      app.kubernetes.io/version: 2.17.2
      scaledobject.keda.sh/name: newcustomer3025-backend-scaler
      tenant: newcustomer3025
    name: keda-hpa-newcustomer3025-backend-scaler
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: keda.sh/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: ScaledObject
      name: newcustomer3025-backend-scaler
      uid: 32a4b44b-feee-433a-b3ad-90e74cf37d8c
    resourceVersion: "8598282"
    uid: 217c8daa-828b-4209-85e7-1a2dc85e03a4
  spec:
    behavior:
      scaleDown:
        policies:
        - periodSeconds: 60
          type: Percent
          value: 50
        selectPolicy: Max
        stabilizationWindowSeconds: 300
      scaleUp:
        policies:
        - periodSeconds: 30
          type: Percent
          value: 100
        - periodSeconds: 30
          type: Pods
          value: 2
        selectPolicy: Max
        stabilizationWindowSeconds: 0
    maxReplicas: 20
    metrics:
    - external:
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newcustomer3025-backend-scaler
        target:
          averageValue: "50"
          type: AverageValue
      type: External
    - external:
        metric:
          name: s3-rabbitmq-tenant-queue
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newcustomer3025-backend-scaler
        target:
          averageValue: "5"
          type: AverageValue
      type: External
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 2
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: newcustomer3025-backend
  status:
    conditions:
    - lastTransitionTime: "2025-07-30T10:54:09Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-30T17:25:17Z"
      message: 'the HPA was unable to compute the replica count: unable to get external
        metric tenant-newcustomer3025/s3-rabbitmq-tenant-queue/&LabelSelector{MatchLabels:map[string]string{scaledobject.keda.sh/name:
        newcustomer3025-backend-scaler,},MatchExpressions:[]LabelSelectorRequirement{},}:
        unable to fetch metrics from external metrics API: rpc error: code = Unknown
        desc = error when getting metric values metric:s3-rabbitmq-tenant-queue encountered
        error'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-07-30T10:54:39Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
    currentMetrics:
    - external:
        current:
          averageValue: "0"
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newcustomer3025-backend-scaler
      type: External
    - type: ""
    - resource:
        current:
          averageUtilization: 1
          averageValue: 2m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 4
          averageValue: "16240640"
        name: memory
      type: Resource
    currentReplicas: 2
    desiredReplicas: 2
    lastScaleTime: "2025-07-30T10:54:09Z"
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"keda.sh/v1alpha1","kind":"ScaledObject","metadata":{"annotations":{},"labels":{"app":"newcustomer3025-frontend","tenant":"newcustomer3025"},"name":"newcustomer3025-frontend-scaler","namespace":"tenant-newcustomer3025"},"spec":{"cooldownPeriod":300,"maxReplicaCount":10,"minReplicaCount":1,"pollingInterval":15,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"newcustomer3025-frontend"},"triggers":[{"metadata":{"value":"70"},"metricType":"Utilization","type":"cpu"},{"metadata":{"value":"80"},"metricType":"Utilization","type":"memory"},{"metadata":{"metricName":"http_requests_per_second","query":"sum(rate(istio_requests_total{destination_service_name=\"newcustomer3025-frontend\"}[2m]))","serverAddress":"http://prometheus-server.monitoring.svc.cluster.local:9090","threshold":"30"},"type":"prometheus"}]}}
    creationTimestamp: "2025-07-30T10:53:56Z"
    labels:
      app: newcustomer3025-frontend
      app.kubernetes.io/managed-by: keda-operator
      app.kubernetes.io/name: keda-hpa-newcustomer3025-frontend-scaler
      app.kubernetes.io/part-of: newcustomer3025-frontend-scaler
      app.kubernetes.io/version: 2.17.2
      scaledobject.keda.sh/name: newcustomer3025-frontend-scaler
      tenant: newcustomer3025
    name: keda-hpa-newcustomer3025-frontend-scaler
    namespace: tenant-newcustomer3025
    ownerReferences:
    - apiVersion: keda.sh/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: ScaledObject
      name: newcustomer3025-frontend-scaler
      uid: e5b5901d-f872-4354-a675-6034c58e7801
    resourceVersion: "8361657"
    uid: ea3a51cd-be03-4f1a-b8b6-810ab272ac47
  spec:
    maxReplicas: 10
    metrics:
    - external:
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newcustomer3025-frontend-scaler
        target:
          averageValue: "30"
          type: AverageValue
      type: External
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 1
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: newcustomer3025-frontend
  status:
    conditions:
    - lastTransitionTime: "2025-07-30T10:54:11Z"
      message: recommended size matches current size
      reason: ReadyForNewScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-30T17:40:21Z"
      message: the HPA was able to successfully calculate a replica count from cpu
        resource utilization (percentage of request)
      reason: ValidMetricFound
      status: "True"
      type: ScalingActive
    - lastTransitionTime: "2025-07-30T10:54:11Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
    currentMetrics:
    - external:
        current:
          averageValue: "0"
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: newcustomer3025-frontend-scaler
      type: External
    - resource:
        current:
          averageUtilization: 2
          averageValue: 1m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 4
          averageValue: "3162112"
        name: memory
      type: Resource
    currentReplicas: 1
    desiredReplicas: 1
kind: List
metadata:
  resourceVersion: ""
