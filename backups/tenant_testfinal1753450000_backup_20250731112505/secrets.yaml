apiVersion: v1
items:
- apiVersion: v1
  data:
    DB_HOST: cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=
    DB_NAME: dGVuYW50X3Rlc3RmaW5hbDE3NTM0NTAwMDA=
    DB_PASSWORD: UDdPSnZRTDloKzNWVFBrZyU=
    DB_PORT: MzMwNg==
    DB_SSL: dHJ1ZQ==
    DB_SSL_CA: L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==
    DB_SSL_MODE: UkVRVUlSRUQ=
    DB_SSL_VERIFY: ZmFsc2U=
    DB_USER: YWRtaW4=
  kind: Secret
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","data":{"DB_HOST":"cHJvZHVjdGlvbi1hdXJvcmEtc2VydmVybGVzcy5jbHVzdGVyLWNwbWFnd2tpMmt2OC5ldS1jZW50cmFsLTEucmRzLmFtYXpvbmF3cy5jb20=","DB_NAME":"dGVuYW50X3Rlc3RmaW5hbDE3NTM0NTAwMDA=","DB_PASSWORD":"UDdPSnZRTDloKzNWVFBrZyU=","DB_PORT":"MzMwNg==","DB_SSL":"dHJ1ZQ==","DB_SSL_CA":"L3RtcC9yZHMtY2EtMjAxOS1yb290LnBlbQ==","DB_SSL_MODE":"UkVRVUlSRUQ=","DB_SSL_VERIFY":"ZmFsc2U=","DB_USER":"YWRtaW4="},"kind":"Secret","metadata":{"annotations":{},"creationTimestamp":null,"name":"testfinal1753450000-db-secret","namespace":"tenant-testfinal1753450000"}}
    creationTimestamp: "2025-07-25T13:59:22Z"
    name: testfinal1753450000-db-secret
    namespace: tenant-testfinal1753450000
    resourceVersion: "5570967"
    uid: 1614524d-4a36-4929-b578-16ffad191502
  type: Opaque
kind: List
metadata:
  resourceVersion: ""
