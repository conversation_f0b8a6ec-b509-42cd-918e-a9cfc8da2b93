apiVersion: v1
items:
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-12T12:42:00+02:00"
    creationTimestamp: "2025-07-12T10:42:00Z"
    generateName: test-security-verification-backend-84ccc957d-
    generation: 1
    labels:
      app: test-security-verification-backend
      component: backend
      pod-template-hash: 84ccc957d
      tenant: test-security-verification
    name: test-security-verification-backend-84ccc957d-9mnmp
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: test-security-verification-backend-84ccc957d
      uid: 397111a2-9036-42eb-b4c6-62049440b0ef
    resourceVersion: "24761646"
    uid: e8b2c05c-b6d7-411c-90a1-3aca38b058c3
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: test-security-verification
      - name: DB_HOST
        value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
      - name: DB_PORT
        value: "3306"
      - name: DB_NAME
        value: architrave
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: test-security-verification-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: test-security-verification-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: test-security-verification-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: test-security-verification-secret
      - name: DB_SSL_MODE
        valueFrom:
          secretKeyRef:
            key: DB_SSL_MODE
            name: test-security-verification-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@test-security-verification-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
    - image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        echo "✅ PHP environment validation completed successfully"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
    nodeName: ip-10-0-10-100.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-kq4xc
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:42:02Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:42:10Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:13Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:13Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:42:00Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://2cbffc27c2c53c6cffd3341b6110d78fa8658b9e3fd6d643658df6aa7d18c0aa
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-12T10:42:10Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://4ceea53048d7ab50179d10f0aba66179270f3ab0643cfd23b62307b132dad95e
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-12T10:42:10Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://e480a589b087eae115bfc92b8b4ca515f99269d13bc1a139de01c46726751c37
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:9a1ed35addb45476afa911696297f8e115993df459278ed036182dd2cd22b67b
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://e480a589b087eae115bfc92b8b4ca515f99269d13bc1a139de01c46726751c37
          exitCode: 0
          finishedAt: "2025-07-12T10:42:02Z"
          reason: Completed
          startedAt: "2025-07-12T10:42:01Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://1832df7e2b57116b01f4d72c9f9907dc555988bf4215f7119ceaf6298bf44cfa
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://1832df7e2b57116b01f4d72c9f9907dc555988bf4215f7119ceaf6298bf44cfa
          exitCode: 0
          finishedAt: "2025-07-12T10:42:06Z"
          reason: Completed
          startedAt: "2025-07-12T10:42:02Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://5b39728d6454178b149792cad447e388299c80ebbe37c7eac1475d026e37b6da
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://5b39728d6454178b149792cad447e388299c80ebbe37c7eac1475d026e37b6da
          exitCode: 0
          finishedAt: "2025-07-12T10:42:07Z"
          reason: Completed
          startedAt: "2025-07-12T10:42:07Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://b0c49f5f796ea34d1ab68d36c78c5047d38adffaa4c174c4ef436ce4a358db40
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://b0c49f5f796ea34d1ab68d36c78c5047d38adffaa4c174c4ef436ce4a358db40
          exitCode: 0
          finishedAt: "2025-07-12T10:42:09Z"
          reason: Completed
          startedAt: "2025-07-12T10:42:08Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://03138f4e44d1227de43e6bd54d93cf493acc1fa91e87abf9f4595dfa1c4fe78a
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://03138f4e44d1227de43e6bd54d93cf493acc1fa91e87abf9f4595dfa1c4fe78a
          exitCode: 0
          finishedAt: "2025-07-12T10:42:10Z"
          reason: Completed
          startedAt: "2025-07-12T10:42:10Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kq4xc
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-12T10:42:00Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-12T12:42:00+02:00"
    creationTimestamp: "2025-07-12T10:44:27Z"
    generateName: test-security-verification-backend-84ccc957d-
    generation: 1
    labels:
      app: test-security-verification-backend
      component: backend
      pod-template-hash: 84ccc957d
      tenant: test-security-verification
    name: test-security-verification-backend-84ccc957d-m6jtl
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: test-security-verification-backend-84ccc957d
      uid: 397111a2-9036-42eb-b4c6-62049440b0ef
    resourceVersion: "24762748"
    uid: 742ee589-5d4b-4c9f-bb7b-c7a94522b345
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: test-security-verification
      - name: DB_HOST
        value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
      - name: DB_PORT
        value: "3306"
      - name: DB_NAME
        value: architrave
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: test-security-verification-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: test-security-verification-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: test-security-verification-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: test-security-verification-secret
      - name: DB_SSL_MODE
        valueFrom:
          secretKeyRef:
            key: DB_SSL_MODE
            name: test-security-verification-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@test-security-verification-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
    - image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        echo "✅ PHP environment validation completed successfully"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
    nodeName: ip-10-0-10-100.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-hx9vn
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:44:29Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:44:38Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:45:41Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:45:41Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:44:27Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://ed1085ce431eba277d58a4b34b9c1dfa6e82a15ea26ec56dc647d8ed1862964a
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-12T10:44:38Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://a664e83fbafd30eb1d71fc9f955dd12a7ce380337060baa6de349e24f46219f5
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-12T10:44:38Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://ceb363735a3ddd9776884856f927149f15740e24be0bb9d5fcf2e244d3223277
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:9a1ed35addb45476afa911696297f8e115993df459278ed036182dd2cd22b67b
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://ceb363735a3ddd9776884856f927149f15740e24be0bb9d5fcf2e244d3223277
          exitCode: 0
          finishedAt: "2025-07-12T10:44:28Z"
          reason: Completed
          startedAt: "2025-07-12T10:44:28Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://1a61f69877dc64a90c60be5db5e7584fbd24998e4895021cc55b2d476d458d21
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://1a61f69877dc64a90c60be5db5e7584fbd24998e4895021cc55b2d476d458d21
          exitCode: 0
          finishedAt: "2025-07-12T10:44:33Z"
          reason: Completed
          startedAt: "2025-07-12T10:44:29Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://ac8c534310bfa5bd171fdc505eddd70734d9c5beb7c0c90eee8b0affded7d786
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://ac8c534310bfa5bd171fdc505eddd70734d9c5beb7c0c90eee8b0affded7d786
          exitCode: 0
          finishedAt: "2025-07-12T10:44:35Z"
          reason: Completed
          startedAt: "2025-07-12T10:44:35Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://09910faf538f97bf1faa348993eef2bac200cf43f46204d1a3a4a536885c9735
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://09910faf538f97bf1faa348993eef2bac200cf43f46204d1a3a4a536885c9735
          exitCode: 0
          finishedAt: "2025-07-12T10:44:36Z"
          reason: Completed
          startedAt: "2025-07-12T10:44:36Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://6bf77f36a1116a1096b66b75c21ebc4c5a29486bb897ad2dea02033b05022631
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://6bf77f36a1116a1096b66b75c21ebc4c5a29486bb897ad2dea02033b05022631
          exitCode: 0
          finishedAt: "2025-07-12T10:44:38Z"
          reason: Completed
          startedAt: "2025-07-12T10:44:38Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-hx9vn
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: **********
    podIPs:
    - ip: **********
    qosClass: Burstable
    startTime: "2025-07-12T10:44:27Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-12T12:43:14+02:00"
    creationTimestamp: "2025-07-12T10:43:14Z"
    generateName: test-security-verification-frontend-858b4dcd4b-
    generation: 1
    labels:
      app: test-security-verification-frontend
      component: frontend
      pod-template-hash: 858b4dcd4b
      tenant: test-security-verification
    name: test-security-verification-frontend-858b4dcd4b-dvw84
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: test-security-verification-frontend-858b4dcd4b
      uid: 927388db-da29-4ed2-ae3f-4215cc7dcb81
    resourceVersion: "24761709"
    uid: eb360d6b-edfd-405d-a2ab-b94fcfbb7f58
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: test-security-verification
      - name: DOMAIN
        value: architrave-assets.com
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: BACKEND_URL
        value: http://test-security-verification-backend-service:8080
      - name: NGINX_LISTEN_PORT
        value: "80"
      - name: NGINX_LISTEN_PROTOCOL
        value: http
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
      imagePullPolicy: IfNotPresent
      name: frontend
      ports:
      - containerPort: 80
        protocol: TCP
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-krqct
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-11-143.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-krqct
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:15Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:14Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:15Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:15Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:14Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://706dfe085ab9c2858f8590b79b41c4e94c9e2647fbc3ac6f34ee8165b7c3685a
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:d1cfc0a8648d5525984ff0ef1ff56d5aa8382b75efc4578cf3906fdaa80deea6
      lastState: {}
      name: frontend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-12T10:43:15Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-krqct
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    phase: Running
    podIP: **********
    podIPs:
    - ip: **********
    qosClass: BestEffort
    startTime: "2025-07-12T10:43:14Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-12T12:43:14+02:00"
    creationTimestamp: "2025-07-12T10:43:15Z"
    generateName: test-security-verification-frontend-858b4dcd4b-
    generation: 1
    labels:
      app: test-security-verification-frontend
      component: frontend
      pod-template-hash: 858b4dcd4b
      tenant: test-security-verification
    name: test-security-verification-frontend-858b4dcd4b-f7q8q
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: test-security-verification-frontend-858b4dcd4b
      uid: 927388db-da29-4ed2-ae3f-4215cc7dcb81
    resourceVersion: "24761749"
    uid: 9447239b-730b-4172-9955-12ebbad58e22
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: test-security-verification
      - name: DOMAIN
        value: architrave-assets.com
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: BACKEND_URL
        value: http://test-security-verification-backend-service:8080
      - name: NGINX_LISTEN_PORT
        value: "80"
      - name: NGINX_LISTEN_PROTOCOL
        value: http
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
      imagePullPolicy: IfNotPresent
      name: frontend
      ports:
      - containerPort: 80
        protocol: TCP
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-md7hg
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-11-143.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-md7hg
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:16Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:16Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:16Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:16Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:43:16Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://fdead0aec47c029a68dc322d39768ab11105b7b1008cc20846bb06aec3f1f8cd
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:d1cfc0a8648d5525984ff0ef1ff56d5aa8382b75efc4578cf3906fdaa80deea6
      lastState: {}
      name: frontend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-12T10:43:16Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-md7hg
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: BestEffort
    startTime: "2025-07-12T10:43:16Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Pod","metadata":{"annotations":{},"labels":{"app":"test-security-verification-healthcheck","tenant":"test-security-verification"},"name":"test-security-verification-healthcheck","namespace":"tenant-test-security-verification"},"spec":{"containers":[{"command":["sh","-c","echo \"Starting healthcheck for tenant test-security-verification...\"\nwhile true; do\n  echo \"Checking frontend service...\"\n  if curl -f -s http://test-security-verification-frontend-service.tenant-test-security-verification.svc.cluster.local:80/ \u003e /dev/null 2\u003e\u00261; then\n    echo \"Frontend service is healthy\"\n  else\n    echo \"Frontend service check failed, but continuing...\"\n  fi\n\n  echo \"Checking backend service...\"\n  if curl -f -s http://test-security-verification-backend-service.tenant-test-security-verification.svc.cluster.local:8080/ \u003e /dev/null 2\u003e\u00261; then\n    echo \"Backend service is healthy\"\n  else\n    echo \"Backend service check failed, but continuing...\"\n  fi\n\n  echo \"Health check completed, sleeping for 60 seconds...\"\n  sleep 60\ndone\n"],"image":"curlimages/curl:latest","name":"healthcheck","resources":{"limits":{"cpu":"50m","memory":"64Mi"},"requests":{"cpu":"10m","memory":"32Mi"}}}],"restartPolicy":"Always"}}
    creationTimestamp: "2025-07-12T10:42:37Z"
    generation: 1
    labels:
      app: test-security-verification-healthcheck
      tenant: test-security-verification
    name: test-security-verification-healthcheck
    namespace: tenant-test-security-verification
    resourceVersion: "24761366"
    uid: 112335b8-5302-465b-be12-c1cb2e656bc9
  spec:
    containers:
    - command:
      - sh
      - -c
      - |
        echo "Starting healthcheck for tenant test-security-verification..."
        while true; do
          echo "Checking frontend service..."
          if curl -f -s http://test-security-verification-frontend-service.tenant-test-security-verification.svc.cluster.local:80/ > /dev/null 2>&1; then
            echo "Frontend service is healthy"
          else
            echo "Frontend service check failed, but continuing..."
          fi

          echo "Checking backend service..."
          if curl -f -s http://test-security-verification-backend-service.tenant-test-security-verification.svc.cluster.local:8080/ > /dev/null 2>&1; then
            echo "Backend service is healthy"
          else
            echo "Backend service check failed, but continuing..."
          fi

          echo "Health check completed, sleeping for 60 seconds..."
          sleep 60
        done
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: healthcheck
      resources:
        limits:
          cpu: 50m
          memory: 64Mi
        requests:
          cpu: 10m
          memory: 32Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-zsqf9
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-10-100.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-zsqf9
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:42:39Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:42:37Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:42:39Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:42:39Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:42:37Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://66cf1237b8dfd8116774b342f74203d19e08d87efa8de7e00ef06bb98a277751
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:9a1ed35addb45476afa911696297f8e115993df459278ed036182dd2cd22b67b
      lastState: {}
      name: healthcheck
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-12T10:42:39Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-zsqf9
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-12T10:42:37Z"
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-12T10:40:13Z"
    generateName: test-security-verification-rabbitmq-754c464f97-
    generation: 1
    labels:
      app: test-security-verification-rabbitmq
      component: rabbitmq
      pod-template-hash: 754c464f97
      tenant: test-security-verification
    name: test-security-verification-rabbitmq-754c464f97-ckhln
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: test-security-verification-rabbitmq-754c464f97
      uid: 1f7baf61-415b-4dcc-8ec6-fb07509215a2
    resourceVersion: "24760343"
    uid: 1aea46af-b8ce-4256-a253-4d6ffc607652
  spec:
    containers:
    - env:
      - name: RABBITMQ_DEFAULT_USER
        value: guest
      - name: RABBITMQ_DEFAULT_PASS
        value: guest
      - name: TENANT_ID
        value: test-security-verification
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imagePullPolicy: IfNotPresent
      name: rabbitmq
      ports:
      - containerPort: 80
        protocol: TCP
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-dtfc8
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-11-143.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-dtfc8
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:40:14Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:40:13Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:40:14Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:40:14Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-12T10:40:13Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://a909517283d605277cd09c9828d2ae2d2814c1a0e489edb440f8581267f89cd0
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev@sha256:a40d91656430a34f8ee2dfbac222d974915ef9d448f7e99ecb4118218a8a84c0
      lastState: {}
      name: rabbitmq
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-12T10:40:14Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-dtfc8
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    phase: Running
    podIP: **********
    podIPs:
    - ip: **********
    qosClass: BestEffort
    startTime: "2025-07-12T10:40:13Z"
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"test-security-verification-backend","component":"backend","tenant":"test-security-verification"},"name":"test-security-verification-backend-service","namespace":"tenant-test-security-verification"},"spec":{"ports":[{"name":"http","port":8080,"protocol":"TCP","targetPort":8080}],"selector":{"app":"test-security-verification-backend","component":"backend","tenant":"test-security-verification"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-12T10:42:59Z"
    labels:
      app: test-security-verification-backend
      component: backend
      tenant: test-security-verification
    name: test-security-verification-backend-service
    namespace: tenant-test-security-verification
    resourceVersion: "24761503"
    uid: f7dac4fa-0a1a-48f0-adf9-a2a859ae81a1
  spec:
    clusterIP: ************
    clusterIPs:
    - ************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
    selector:
      app: test-security-verification-backend
      component: backend
      tenant: test-security-verification
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"test-security-verification-frontend","component":"frontend","tenant":"test-security-verification"},"name":"test-security-verification-frontend-service","namespace":"tenant-test-security-verification"},"spec":{"ports":[{"port":80,"protocol":"TCP","targetPort":80}],"selector":{"app":"test-security-verification-frontend","component":"frontend","tenant":"test-security-verification"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-12T10:43:01Z"
    labels:
      app: test-security-verification-frontend
      component: frontend
      tenant: test-security-verification
    name: test-security-verification-frontend-service
    namespace: tenant-test-security-verification
    resourceVersion: "24761545"
    uid: 0d780512-9e8c-44cd-8241-e32247ac8a89
  spec:
    clusterIP: ************
    clusterIPs:
    - ************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 80
      protocol: TCP
      targetPort: 80
    selector:
      app: test-security-verification-frontend
      component: frontend
      tenant: test-security-verification
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"test-security-verification-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"test-security-verification"},"name":"test-security-verification-rabbitmq-mgmt-service","namespace":"tenant-test-security-verification"},"spec":{"ports":[{"port":15672,"protocol":"TCP","targetPort":15672}],"selector":{"app":"test-security-verification-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"test-security-verification"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-12T10:40:14Z"
    labels:
      app: test-security-verification-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: test-security-verification
    name: test-security-verification-rabbitmq-mgmt-service
    namespace: tenant-test-security-verification
    resourceVersion: "24761578"
    uid: 8545c8d0-210a-495f-b8a2-36a86115a723
  spec:
    clusterIP: **************
    clusterIPs:
    - **************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 15672
      protocol: TCP
      targetPort: 15672
    selector:
      app: test-security-verification-rabbitmq
      component: rabbitmq
      tenant: test-security-verification
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"test-security-verification-rabbitmq","component":"rabbitmq","tenant":"test-security-verification"},"name":"test-security-verification-rabbitmq-service","namespace":"tenant-test-security-verification"},"spec":{"ports":[{"port":5672,"protocol":"TCP","targetPort":5672}],"selector":{"app":"test-security-verification-rabbitmq","component":"rabbitmq","tenant":"test-security-verification"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-12T10:40:14Z"
    labels:
      app: test-security-verification-rabbitmq
      component: rabbitmq
      tenant: test-security-verification
    name: test-security-verification-rabbitmq-service
    namespace: tenant-test-security-verification
    resourceVersion: "24760339"
    uid: 9193b489-2793-493d-b785-faa610c85620
  spec:
    clusterIP: **************
    clusterIPs:
    - **************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 5672
      protocol: TCP
      targetPort: 5672
    selector:
      app: test-security-verification-rabbitmq
      component: rabbitmq
      tenant: test-security-verification
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"test-security-verification-webapp","component":"webapp","tenant":"test-security-verification"},"name":"webapp","namespace":"tenant-test-security-verification"},"spec":{"ports":[{"port":80,"protocol":"TCP","targetPort":8080}],"selector":{"app":"test-security-verification-backend","component":"backend","tenant":"test-security-verification"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-12T10:43:02Z"
    labels:
      app: test-security-verification-webapp
      component: webapp
      tenant: test-security-verification
    name: webapp
    namespace: tenant-test-security-verification
    resourceVersion: "24761567"
    uid: a19fb189-0f26-418a-949b-bab74ea3e2eb
  spec:
    clusterIP: **************
    clusterIPs:
    - **************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 80
      protocol: TCP
      targetPort: 8080
    selector:
      app: test-security-verification-backend
      component: backend
      tenant: test-security-verification
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "2"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"test-security-verification-backend","component":"backend","tenant":"test-security-verification"},"name":"test-security-verification-backend","namespace":"tenant-test-security-verification"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"test-security-verification-backend","component":"backend","tenant":"test-security-verification"}},"template":{"metadata":{"labels":{"app":"test-security-verification-backend","component":"backend","tenant":"test-security-verification"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"test-security-verification"},{"name":"DB_HOST","value":"production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"},{"name":"DB_PORT","value":"3306"},{"name":"DB_NAME","value":"architrave"},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"DB_USER","name":"test-security-verification-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"DB_PASSWORD","name":"test-security-verification-secret"}}},{"name":"DB_SSL","valueFrom":{"secretKeyRef":{"key":"DB_SSL","name":"test-security-verification-secret"}}},{"name":"DB_SSL_CA","valueFrom":{"secretKeyRef":{"key":"DB_SSL_CA","name":"test-security-verification-secret"}}},{"name":"DB_SSL_MODE","valueFrom":{"secretKeyRef":{"key":"DB_SSL_MODE","name":"test-security-verification-secret"}}},{"name":"RABBITMQ_URL","value":"amqp://guest:guest@test-security-verification-rabbitmq-service:5672/"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","livenessProbe":{"initialDelaySeconds":60,"periodSeconds":30,"tcpSocket":{"port":9000}},"name":"backend","ports":[{"containerPort":9000}],"readinessProbe":{"initialDelaySeconds":30,"periodSeconds":30,"tcpSocket":{"port":9000}},"resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}},"volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"},{"mountPath":"/shared-app","name":"shared-app"}]},{"image":"nginx:1.21-alpine","livenessProbe":{"failureThreshold":5,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-liveness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":90,"periodSeconds":60,"successThreshold":1,"timeoutSeconds":10},"name":"nginx","ports":[{"containerPort":8080}],"readinessProbe":{"failureThreshold":10,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-readiness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"timeoutSeconds":5},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"},{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Downloading RDS CA certificate...\"\ncurl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem\necho \"SSL certificate downloaded successfully\"\n"],"image":"curlimages/curl:latest","name":"ssl-cert-downloader","volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"}]},{"command":["sh","-c","echo \"Copying application files from /storage/ArchAssets to shared volume...\"\nif [ -d \"/storage/ArchAssets\" ]; then\n    cp -r /storage/ArchAssets/* /shared-app/ 2\u003e/dev/null || true\n    echo \"Application files copied successfully from /storage/ArchAssets\"\n    echo \"Copied files:\"\n    ls -la /shared-app/ | head -10\nelse\n    echo \"Warning: /storage/ArchAssets directory not found in webapp_dev image\"\n    echo \"Available directories in /storage:\"\n    ls -la /storage/ 2\u003e/dev/null || echo \"No /storage directory\"\n    echo \"Available directories in root:\"\n    ls -la / | grep -E \"(var|storage|app)\" || echo \"No relevant directories found\"\nfi\necho \"Application files copy process completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"app-files-copier","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Fixing PHP application config path issues and PHP-FPM compatibility...\"\n\n# Fix the relative path issue in /shared-app/public/api/index.php\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Found API index.php, fixing config path...\"\n\n  # Create backup\n  cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup\n\n  # Fix the relative path issue by using absolute path\n  sed -i \"s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g\" /shared-app/public/api/index.php\n\n  echo \"Config path fixed in API index.php\"\n  echo \"Checking the fix:\"\n  grep -n \"require.*config.*application.config.php\" /shared-app/public/api/index.php || echo \"Pattern not found after fix\"\nelse\n  echo \"Warning: /shared-app/public/api/index.php not found\"\n  echo \"Available files in /shared-app/public/:\"\n  ls -la /shared-app/public/ 2\u003e/dev/null || echo \"No public directory\"\nfi\n\n# Create PHP-FPM compatibility polyfill for apache_request_headers()\necho \"Creating PHP-FPM compatibility polyfill...\"\ncat \u003e /shared-app/php_fpm_polyfill.php \u003c\u003c 'POLYFILL_EOF'\n\u003c?php\n/**\n * PHP-FPM Compatibility Polyfill for Apache functions\n * This file provides missing Apache functions for PHP-FPM + nginx environments\n *\n * NOTE: Using forced function definition without conditional checks\n * because function_exists() may return false positives in some PHP-FPM environments\n */\n\n/**\n * Polyfill for apache_request_headers() function in PHP-FPM environment\n * Only define if the function doesn't already exist\n */\nif (!function_exists('apache_request_headers')) {\n    function apache_request_headers() {\n        $headers = array();\n\n        // Get all HTTP headers from $_SERVER superglobal\n        foreach ($_SERVER as $key =\u003e $value) {\n            if (strpos($key, 'HTTP_') === 0) {\n                // Convert HTTP_HEADER_NAME to Header-Name format\n                $header = str_replace('_', '-', substr($key, 5));\n                $header = ucwords(strtolower($header), '-');\n                $headers[$header] = $value;\n            }\n        }\n\n        // Add special headers that might not have HTTP_ prefix\n        if (isset($_SERVER['CONTENT_TYPE'])) {\n            $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];\n        }\n        if (isset($_SERVER['CONTENT_LENGTH'])) {\n            $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];\n        }\n\n        return $headers;\n    }\n}\n\n/**\n * Polyfill for apache_response_headers() function in PHP-FPM environment\n * @return array\n */\nfunction apache_response_headers() {\n    $headers = array();\n\n    // Get headers that were set with header() function\n    if (function_exists('headers_list')) {\n        foreach (headers_list() as $header) {\n            $parts = explode(':', $header, 2);\n            if (count($parts) === 2) {\n                $headers[trim($parts[0])] = trim($parts[1]);\n            }\n        }\n    }\n\n    return $headers;\n}\n\n/**\n * Alias for apache_request_headers() - commonly used alternative\n * Only define if the function doesn't already exist\n */\nif (!function_exists('getallheaders')) {\n    function getallheaders() {\n        return apache_request_headers();\n    }\n}\nPOLYFILL_EOF\n\necho \"✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php\"\n\n# Inject the polyfill into the main bootstrap file\nif [ -f \"/shared-app/bootstrap.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into bootstrap.php...\"\n\n  # Create backup\n  cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bootstrap.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into bootstrap.php\"\nelse\n  echo \"Warning: /shared-app/bootstrap.php not found\"\nfi\n\n# Also inject into the API index.php as a fallback\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into API index.php as fallback...\"\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/public/api/index.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into API index.php\"\nfi\n\n# Verify config file exists\nif [ -f \"/shared-app/config/application.config.php\" ]; then\n  echo \"✅ Config file exists at /shared-app/config/application.config.php\"\nelse\n  echo \"❌ Config file missing at /shared-app/config/application.config.php\"\n  echo \"Available files in /shared-app/config/:\"\n  ls -la /shared-app/config/ 2\u003e/dev/null || echo \"No config directory\"\nfi\n\necho \"✅ PHP config path fix and PHP-FPM compatibility setup completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-config-fixer","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Validating PHP environment for PHP-FPM + nginx compatibility...\"\n\n# Test if polyfill was loaded correctly\nphp -r \"\nrequire_once '/shared-app/php_fpm_polyfill.php';\n\necho 'Testing PHP-FPM polyfill functions...\\n';\n\n// Test apache_request_headers function\nif (function_exists('apache_request_headers')) {\n    echo '✅ apache_request_headers() function is available\\n';\n} else {\n    echo '❌ apache_request_headers() function is missing\\n';\n    exit(1);\n}\n\n// Test getallheaders function\nif (function_exists('getallheaders')) {\n    echo '✅ getallheaders() function is available\\n';\n} else {\n    echo '❌ getallheaders() function is missing\\n';\n    exit(1);\n}\n\n// Test apache_response_headers function\nif (function_exists('apache_response_headers')) {\n    echo '✅ apache_response_headers() function is available\\n';\n} else {\n    echo '❌ apache_response_headers() function is missing\\n';\n    exit(1);\n}\n\necho '✅ All PHP-FPM polyfill functions are working correctly\\n';\n\"\n\n# Test basic PHP application loading\necho \"Testing PHP application bootstrap loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    echo '✅ Bootstrap loaded successfully\\n';\n} catch (Exception \\$e) {\n    echo '❌ Bootstrap loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# Test Laminas application config loading\necho \"Testing Laminas application config loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    \\$config = require '/shared-app/config/application.config.php';\n    echo '✅ Application config loaded successfully\\n';\n    echo 'Config modules: ' . count(\\$config['modules']) . '\\n';\n} catch (Exception \\$e) {\n    echo '❌ Application config loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\necho \"✅ PHP environment validation completed successfully\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-env-validator","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Creating HTTP nginx configuration for port 8080...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 8080;\n    server_name localhost;\n    root /shared-app/public;\n    index index.php index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /api/health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # Root endpoint - redirect to API\n    location = / {\n        return 302 /api/;\n    }\n\n    location / {\n        try_files $uri $uri/ /api/index.php?$args;\n    }\n\n    location ~ \\.php$ {\n        fastcgi_pass localhost:9000;\n        fastcgi_index index.php;\n        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\n        fastcgi_param AUTOMATED_TEST 1;\n        include fastcgi_params;\n        fastcgi_read_timeout 310;\n    }\n\n    location ~ /\\.ht {\n        deny all;\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"ssl-certs"},{"emptyDir":{},"name":"shared-app"},{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-12T10:40:15Z"
    generation: 3
    labels:
      app: test-security-verification-backend
      component: backend
      tenant: test-security-verification
    name: test-security-verification-backend
    namespace: tenant-test-security-verification
    resourceVersion: "24762754"
    uid: 358de384-b6c8-412e-843b-6c07001b98e8
  spec:
    progressDeadlineSeconds: 600
    replicas: 2
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: test-security-verification-backend
        component: backend
        tenant: test-security-verification
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-12T12:42:00+02:00"
        creationTimestamp: null
        labels:
          app: test-security-verification-backend
          component: backend
          tenant: test-security-verification
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: test-security-verification
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: architrave
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: test-security-verification-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: test-security-verification-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: test-security-verification-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: test-security-verification-secret
          - name: DB_SSL_MODE
            valueFrom:
              secretKeyRef:
                key: DB_SSL_MODE
                name: test-security-verification-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@test-security-verification-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    conditions:
    - lastTransitionTime: "2025-07-12T10:40:15Z"
      lastUpdateTime: "2025-07-12T10:43:13Z"
      message: ReplicaSet "test-security-verification-backend-84ccc957d" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-12T10:45:41Z"
      lastUpdateTime: "2025-07-12T10:45:41Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 3
    readyReplicas: 2
    replicas: 2
    updatedReplicas: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "2"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"test-security-verification-frontend","component":"frontend","tenant":"test-security-verification"},"name":"test-security-verification-frontend","namespace":"tenant-test-security-verification"},"spec":{"replicas":2,"selector":{"matchLabels":{"app":"test-security-verification-frontend","component":"frontend","tenant":"test-security-verification"}},"template":{"metadata":{"labels":{"app":"test-security-verification-frontend","component":"frontend","tenant":"test-security-verification"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"test-security-verification"},{"name":"DOMAIN","value":"architrave-assets.com"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"},{"name":"BACKEND_URL","value":"http://test-security-verification-backend-service:8080"},{"name":"NGINX_LISTEN_PORT","value":"80"},{"name":"NGINX_LISTEN_PROTOCOL","value":"http"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7","name":"frontend","ports":[{"containerPort":80}]}]}}}}
    creationTimestamp: "2025-07-12T10:43:01Z"
    generation: 2
    labels:
      app: test-security-verification-frontend
      component: frontend
      tenant: test-security-verification
    name: test-security-verification-frontend
    namespace: tenant-test-security-verification
    resourceVersion: "24761765"
    uid: 363c4aa2-384b-4918-83b1-b99fe3ff9c13
  spec:
    progressDeadlineSeconds: 600
    replicas: 2
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: test-security-verification-frontend
        component: frontend
        tenant: test-security-verification
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-12T12:43:14+02:00"
        creationTimestamp: null
        labels:
          app: test-security-verification-frontend
          component: frontend
          tenant: test-security-verification
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: test-security-verification
          - name: DOMAIN
            value: architrave-assets.com
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://test-security-verification-backend-service:8080
          - name: NGINX_LISTEN_PORT
            value: "80"
          - name: NGINX_LISTEN_PROTOCOL
            value: http
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
          imagePullPolicy: IfNotPresent
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 2
    conditions:
    - lastTransitionTime: "2025-07-12T10:43:15Z"
      lastUpdateTime: "2025-07-12T10:43:15Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2025-07-12T10:43:01Z"
      lastUpdateTime: "2025-07-12T10:43:16Z"
      message: ReplicaSet "test-security-verification-frontend-858b4dcd4b" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 2
    readyReplicas: 2
    replicas: 2
    updatedReplicas: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"test-security-verification-rabbitmq","component":"rabbitmq","tenant":"test-security-verification"},"name":"test-security-verification-rabbitmq","namespace":"tenant-test-security-verification"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"test-security-verification-rabbitmq","component":"rabbitmq","tenant":"test-security-verification"}},"template":{"metadata":{"labels":{"app":"test-security-verification-rabbitmq","component":"rabbitmq","tenant":"test-security-verification"}},"spec":{"containers":[{"env":[{"name":"RABBITMQ_DEFAULT_USER","value":"guest"},{"name":"RABBITMQ_DEFAULT_PASS","value":"guest"},{"name":"TENANT_ID","value":"test-security-verification"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02","name":"rabbitmq","ports":[{"containerPort":80}]}]}}}}
    creationTimestamp: "2025-07-12T10:40:13Z"
    generation: 1
    labels:
      app: test-security-verification-rabbitmq
      component: rabbitmq
      tenant: test-security-verification
    name: test-security-verification-rabbitmq
    namespace: tenant-test-security-verification
    resourceVersion: "24760347"
    uid: 40ea0975-7202-48b8-ba1a-b8ea5a3b31d1
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: test-security-verification-rabbitmq
        component: rabbitmq
        tenant: test-security-verification
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: test-security-verification-rabbitmq
          component: rabbitmq
          tenant: test-security-verification
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: test-security-verification
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-12T10:40:14Z"
      lastUpdateTime: "2025-07-12T10:40:14Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2025-07-12T10:40:13Z"
      lastUpdateTime: "2025-07-12T10:40:14Z"
      message: ReplicaSet "test-security-verification-rabbitmq-754c464f97" has successfully
        progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-12T10:42:00Z"
    generation: 2
    labels:
      app: test-security-verification-backend
      component: backend
      pod-template-hash: 84ccc957d
      tenant: test-security-verification
    name: test-security-verification-backend-84ccc957d
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: test-security-verification-backend
      uid: 358de384-b6c8-412e-843b-6c07001b98e8
    resourceVersion: "24762749"
    uid: 397111a2-9036-42eb-b4c6-62049440b0ef
  spec:
    replicas: 2
    selector:
      matchLabels:
        app: test-security-verification-backend
        component: backend
        pod-template-hash: 84ccc957d
        tenant: test-security-verification
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-12T12:42:00+02:00"
        creationTimestamp: null
        labels:
          app: test-security-verification-backend
          component: backend
          pod-template-hash: 84ccc957d
          tenant: test-security-verification
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: test-security-verification
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: architrave
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: test-security-verification-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: test-security-verification-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: test-security-verification-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: test-security-verification-secret
          - name: DB_SSL_MODE
            valueFrom:
              secretKeyRef:
                key: DB_SSL_MODE
                name: test-security-verification-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@test-security-verification-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    fullyLabeledReplicas: 2
    observedGeneration: 2
    readyReplicas: 2
    replicas: 2
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-12T10:40:15Z"
    generation: 2
    labels:
      app: test-security-verification-backend
      component: backend
      pod-template-hash: 887cd587d
      tenant: test-security-verification
    name: test-security-verification-backend-887cd587d
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: test-security-verification-backend
      uid: 358de384-b6c8-412e-843b-6c07001b98e8
    resourceVersion: "24761665"
    uid: 91b3b979-459b-44f8-b656-0edc5dea8ecd
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: test-security-verification-backend
        component: backend
        pod-template-hash: 887cd587d
        tenant: test-security-verification
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: test-security-verification-backend
          component: backend
          pod-template-hash: 887cd587d
          tenant: test-security-verification
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: test-security-verification
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: architrave
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: test-security-verification-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: test-security-verification-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: test-security-verification-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: test-security-verification-secret
          - name: DB_SSL_MODE
            valueFrom:
              secretKeyRef:
                key: DB_SSL_MODE
                name: test-security-verification-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@test-security-verification-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-12T10:43:01Z"
    generation: 3
    labels:
      app: test-security-verification-frontend
      component: frontend
      pod-template-hash: 68bdcd6666
      tenant: test-security-verification
    name: test-security-verification-frontend-68bdcd6666
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: test-security-verification-frontend
      uid: 363c4aa2-384b-4918-83b1-b99fe3ff9c13
    resourceVersion: "24761764"
    uid: f6366281-821a-4455-aba2-79a8652a7383
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: test-security-verification-frontend
        component: frontend
        pod-template-hash: 68bdcd6666
        tenant: test-security-verification
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: test-security-verification-frontend
          component: frontend
          pod-template-hash: 68bdcd6666
          tenant: test-security-verification
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: test-security-verification
          - name: DOMAIN
            value: architrave-assets.com
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://test-security-verification-backend-service:8080
          - name: NGINX_LISTEN_PORT
            value: "80"
          - name: NGINX_LISTEN_PROTOCOL
            value: http
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
          imagePullPolicy: IfNotPresent
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    observedGeneration: 3
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-12T10:43:14Z"
    generation: 2
    labels:
      app: test-security-verification-frontend
      component: frontend
      pod-template-hash: 858b4dcd4b
      tenant: test-security-verification
    name: test-security-verification-frontend-858b4dcd4b
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: test-security-verification-frontend
      uid: 363c4aa2-384b-4918-83b1-b99fe3ff9c13
    resourceVersion: "24761752"
    uid: 927388db-da29-4ed2-ae3f-4215cc7dcb81
  spec:
    replicas: 2
    selector:
      matchLabels:
        app: test-security-verification-frontend
        component: frontend
        pod-template-hash: 858b4dcd4b
        tenant: test-security-verification
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-12T12:43:14+02:00"
        creationTimestamp: null
        labels:
          app: test-security-verification-frontend
          component: frontend
          pod-template-hash: 858b4dcd4b
          tenant: test-security-verification
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: test-security-verification
          - name: DOMAIN
            value: architrave-assets.com
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://test-security-verification-backend-service:8080
          - name: NGINX_LISTEN_PORT
            value: "80"
          - name: NGINX_LISTEN_PROTOCOL
            value: http
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.7
          imagePullPolicy: IfNotPresent
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 2
    fullyLabeledReplicas: 2
    observedGeneration: 2
    readyReplicas: 2
    replicas: 2
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-12T10:40:13Z"
    generation: 1
    labels:
      app: test-security-verification-rabbitmq
      component: rabbitmq
      pod-template-hash: 754c464f97
      tenant: test-security-verification
    name: test-security-verification-rabbitmq-754c464f97
    namespace: tenant-test-security-verification
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: test-security-verification-rabbitmq
      uid: 40ea0975-7202-48b8-ba1a-b8ea5a3b31d1
    resourceVersion: "24760344"
    uid: 1f7baf61-415b-4dcc-8ec6-fb07509215a2
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: test-security-verification-rabbitmq
        component: rabbitmq
        pod-template-hash: 754c464f97
        tenant: test-security-verification
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: test-security-verification-rabbitmq
          component: rabbitmq
          pod-template-hash: 754c464f97
          tenant: test-security-verification
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: test-security-verification
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"autoscaling/v2","kind":"HorizontalPodAutoscaler","metadata":{"annotations":{},"name":"test-security-verification-backend-hpa","namespace":"tenant-test-security-verification"},"spec":{"behavior":{"scaleDown":{"policies":[{"periodSeconds":60,"type":"Percent","value":10},{"periodSeconds":60,"type":"Pods","value":2}],"selectPolicy":"Min","stabilizationWindowSeconds":300},"scaleUp":{"policies":[{"periodSeconds":60,"type":"Percent","value":100},{"periodSeconds":60,"type":"Pods","value":4}],"selectPolicy":"Max","stabilizationWindowSeconds":60}},"maxReplicas":20,"metrics":[{"resource":{"name":"cpu","target":{"averageUtilization":70,"type":"Utilization"}},"type":"Resource"},{"resource":{"name":"memory","target":{"averageUtilization":80,"type":"Utilization"}},"type":"Resource"}],"minReplicas":2,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"test-security-verification-backend"}}}
    creationTimestamp: "2025-07-12T10:44:11Z"
    name: test-security-verification-backend-hpa
    namespace: tenant-test-security-verification
    resourceVersion: "24780407"
    uid: d103fcd9-7ba7-42a5-8708-28af25f81f9a
  spec:
    behavior:
      scaleDown:
        policies:
        - periodSeconds: 60
          type: Percent
          value: 10
        - periodSeconds: 60
          type: Pods
          value: 2
        selectPolicy: Min
        stabilizationWindowSeconds: 300
      scaleUp:
        policies:
        - periodSeconds: 60
          type: Percent
          value: 100
        - periodSeconds: 60
          type: Pods
          value: 4
        selectPolicy: Max
        stabilizationWindowSeconds: 60
    maxReplicas: 20
    metrics:
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 2
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: test-security-verification-backend
  status:
    conditions:
    - lastTransitionTime: "2025-07-12T10:44:26Z"
      message: recommended size matches current size
      reason: ReadyForNewScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-12T10:44:42Z"
      message: the HPA was able to successfully calculate a replica count from cpu
        resource utilization (percentage of request)
      reason: ValidMetricFound
      status: "True"
      type: ScalingActive
    - lastTransitionTime: "2025-07-12T10:49:42Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
    currentMetrics:
    - resource:
        current:
          averageUtilization: 1
          averageValue: 1m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 10
          averageValue: "34387968"
        name: memory
      type: Resource
    currentReplicas: 2
    desiredReplicas: 2
    lastScaleTime: "2025-07-12T10:44:26Z"
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"autoscaling/v2","kind":"HorizontalPodAutoscaler","metadata":{"annotations":{},"name":"test-security-verification-frontend-hpa","namespace":"tenant-test-security-verification"},"spec":{"behavior":{"scaleDown":{"policies":[{"periodSeconds":60,"type":"Percent","value":25}],"stabilizationWindowSeconds":300},"scaleUp":{"policies":[{"periodSeconds":60,"type":"Percent","value":100}],"stabilizationWindowSeconds":60}},"maxReplicas":10,"metrics":[{"resource":{"name":"cpu","target":{"averageUtilization":70,"type":"Utilization"}},"type":"Resource"},{"resource":{"name":"memory","target":{"averageUtilization":80,"type":"Utilization"}},"type":"Resource"}],"minReplicas":1,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"test-security-verification-frontend"}}}
    creationTimestamp: "2025-07-12T10:44:13Z"
    name: test-security-verification-frontend-hpa
    namespace: tenant-test-security-verification
    resourceVersion: "24780679"
    uid: 3bbdd7e4-7e86-4353-9212-f05e1f714b51
  spec:
    behavior:
      scaleDown:
        policies:
        - periodSeconds: 60
          type: Percent
          value: 25
        selectPolicy: Max
        stabilizationWindowSeconds: 300
      scaleUp:
        policies:
        - periodSeconds: 60
          type: Percent
          value: 100
        selectPolicy: Max
        stabilizationWindowSeconds: 60
    maxReplicas: 10
    metrics:
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 1
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: test-security-verification-frontend
  status:
    conditions:
    - lastTransitionTime: "2025-07-12T10:44:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-12T10:44:28Z"
      message: 'the HPA was unable to compute the replica count: failed to get cpu
        utilization: missing request for cpu in container frontend of Pod test-security-verification-frontend-858b4dcd4b-f7q8q'
      reason: FailedGetResourceMetric
      status: "False"
      type: ScalingActive
    currentMetrics:
    - type: ""
    - type: ""
    currentReplicas: 2
    desiredReplicas: 0
kind: List
metadata:
  resourceVersion: ""
