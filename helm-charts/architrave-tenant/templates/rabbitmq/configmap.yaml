{{- if .Values.rabbitmq.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.tenant.id }}-rabbitmq-config
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: rabbitmq
  annotations:
    use-subpath: "true"
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
data:
  rabbit.config: |
    [
      {rabbit, [
        {default_user, <<"{{ .Values.rabbitmq.config.defaultUser }}">>},
        {default_pass, <<"{{ .Values.rabbitmq.config.defaultPass }}">>}
      ]},
      {kernel, []}
    ].
  enabled_plugins: |
    {{- if .Values.rabbitmq.management.enabled }}
    [rabbitmq_management,rabbitmq_web_stomp].
    {{- else }}
    [rabbitmq_web_stomp].
    {{- end }}
{{- end }}
