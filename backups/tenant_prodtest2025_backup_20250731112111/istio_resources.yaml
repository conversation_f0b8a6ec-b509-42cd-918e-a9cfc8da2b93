apiVersion: v1
items:
- apiVersion: networking.istio.io/v1
  kind: VirtualService
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.istio.io/v1beta1","kind":"VirtualService","metadata":{"annotations":{},"labels":{"managed-by":"tenant-onboarding","tenant":"prodtest2025"},"name":"tenant-prodtest2025-vs","namespace":"tenant-prodtest2025"},"spec":{"gateways":["istio-system/tenant-gateway"],"hosts":["prodtest2025.architrave-assets.com"],"http":[{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"match":[{"uri":{"prefix":"/api"}}],"retries":{"attempts":3,"perTryTimeout":"10s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"prodtest2025-backend-service","port":{"number":8080}}}],"timeout":"30s"},{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"retries":{"attempts":2,"perTryTimeout":"5s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"prodtest2025-frontend-service","port":{"number":80}}}],"timeout":"15s"}]}}
    creationTimestamp: "2025-07-23T20:20:30Z"
    generation: 2
    labels:
      managed-by: tenant-onboarding
      tenant: prodtest2025
    name: tenant-prodtest2025-vs
    namespace: tenant-prodtest2025
    resourceVersion: "7127799"
    uid: 1f1c7eef-b89a-436b-8488-ceb16cb934af
  spec:
    gateways:
    - istio-system/tenant-gateway
    hosts:
    - prodtest2025.architrave-assets.de
    http:
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      match:
      - uri:
          prefix: /api
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: prodtest2025-backend-service
          port:
            number: 8080
      timeout: 30s
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      retries:
        attempts: 2
        perTryTimeout: 5s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: prodtest2025-frontend-service
          port:
            number: 80
      timeout: 15s
kind: List
metadata:
  resourceVersion: ""
