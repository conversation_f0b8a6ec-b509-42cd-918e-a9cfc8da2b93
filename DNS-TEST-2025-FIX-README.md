# DNS-TEST-2025 Fix Implementation

This document outlines the comprehensive fix for the dns-test-2025 tenant deployment issues.

## Issues Identified

1. **S3 CSI Mount Failure**: Node role lacks S3 permissions for bucket `architravetestdb-dns-test-2025`
2. **Missing ServiceAccount IRSA**: ServiceAccount `dns-test-2025-aws-sa` not annotated with IAM role
3. **Empty S3 Bucket**: No web application files in S3 bucket's public/ directory
4. **Secrets Manager Access**: No IRSA role for accessing database credentials
5. **Volume Mount Configuration**: Deployment not using correct PVC and ServiceAccount

## Solution Overview

### Files Created

1. **s3-node-role-policy.json** - IAM policy for node role S3 access
2. **dns-test-2025-s3-csi.yaml** - S3 CSI StorageClass, PV, and PVC
3. **dns-test-2025-serviceaccount.yaml** - ServiceAccount with IRSA annotation
4. **dns-test-2025-irsa-role.json** - IRSA role trust policy template
5. **dns-test-2025-secrets-policy.json** - Secrets Manager access policy
6. **dns-test-2025-deployment-patch.yaml** - Deployment configuration fixes
7. **fix-dns-test-2025.sh** - Main fix script
8. **verify-dns-test-2025-fix.sh** - Verification script
9. **upload-webapp-content.sh** - S3 content upload script

## Step-by-Step Fix Process

### Phase 1: Apply Infrastructure Fixes

```bash
# Run the main fix script
./fix-dns-test-2025.sh
```

This script will:
- Create IRSA role with correct OIDC trust policy
- Attach Secrets Manager permissions to IRSA role
- Add S3 permissions to node role
- Create namespace if needed
- Apply S3 CSI resources
- Create ServiceAccount with IRSA annotation
- Check S3 bucket existence
- Update existing deployment if found

### Phase 2: Upload Web Application Content

```bash
# Upload your webapp files to S3
./upload-webapp-content.sh /path/to/your/webapp/public

# Or create a test index.php and upload
mkdir -p ./temp-webapp/public
cat > ./temp-webapp/public/index.php << 'EOF'
<?php
echo '<h1>DNS Test 2025 - Working!</h1>';
echo '<p>Timestamp: ' . date('Y-m-d H:i:s') . '</p>';
phpinfo();
EOF

./upload-webapp-content.sh ./temp-webapp/public
```

### Phase 3: Apply Deployment Fixes

```bash
# Apply deployment patch
kubectl apply -f dns-test-2025-deployment-patch.yaml

# Restart deployment to pick up changes
kubectl rollout restart deployment/dns-test-2025-webapp -n tenant-dns-test-2025
kubectl rollout status deployment/dns-test-2025-webapp -n tenant-dns-test-2025
```

### Phase 4: Verify Fixes

```bash
# Run verification script
./verify-dns-test-2025-fix.sh
```

## Manual Verification Steps

### 1. Check S3 Mount

```bash
# Check if PVC is bound
kubectl get pvc s3-pvc-dns-test-2025 -n tenant-dns-test-2025

# Check pod mount
kubectl exec -n tenant-dns-test-2025 deployment/dns-test-2025-webapp -c nginx -- ls -la /storage/clear/public/

# Verify index.php exists
kubectl exec -n tenant-dns-test-2025 deployment/dns-test-2025-webapp -c nginx -- cat /storage/clear/public/index.php
```

### 2. Check ServiceAccount and IRSA

```bash
# Verify ServiceAccount annotation
kubectl get serviceaccount dns-test-2025-aws-sa -n tenant-dns-test-2025 -o yaml

# Check if deployment uses correct ServiceAccount
kubectl get deployment dns-test-2025-webapp -n tenant-dns-test-2025 -o jsonpath='{.spec.template.spec.serviceAccountName}'
```

### 3. Test Database Connection

```bash
# Check if secrets are accessible
kubectl exec -n tenant-dns-test-2025 deployment/dns-test-2025-webapp -c backend -- env | grep -E "(SECRET_ID|AWS_)"

# Check if local.php config was created
kubectl exec -n tenant-dns-test-2025 deployment/dns-test-2025-webapp -c backend -- cat /app/config/autoload/local.php
```

### 4. Test Web Access

```bash
# Port forward to test locally
kubectl port-forward -n tenant-dns-test-2025 deployment/dns-test-2025-webapp 8080:8080

# Test in another terminal
curl http://localhost:8080/
```

## Environment Variables Required

Make sure these environment variables are set in your deployment:

```yaml
env:
- name: SECRET_ID
  value: "arn:aws:secretsmanager:eu-central-1:************:secret:production/rds/master-new"
- name: AWS_REGION
  value: "eu-central-1"
```

## Troubleshooting

### S3 Mount Issues

```bash
# Check S3 CSI driver logs
kubectl logs -n kube-system -l app=s3-csi-node

# Check node permissions
aws iam list-attached-role-policies --role-name prod-architrave-eks-node-role
```

### ServiceAccount Issues

```bash
# Check IRSA role
aws iam get-role --role-name dns-test-2025-irsa-role

# Check trust policy
aws iam get-role --role-name dns-test-2025-irsa-role --query 'Role.AssumeRolePolicyDocument'
```

### Pod Issues

```bash
# Check pod events
kubectl describe pod -n tenant-dns-test-2025 -l app=dns-test-2025-webapp

# Check pod logs
kubectl logs -n tenant-dns-test-2025 -l app=dns-test-2025-webapp -c nginx
kubectl logs -n tenant-dns-test-2025 -l app=dns-test-2025-webapp -c backend
```

## Success Criteria

✅ PVC `s3-pvc-dns-test-2025` is Bound  
✅ ServiceAccount `dns-test-2025-aws-sa` has IRSA annotation  
✅ S3 bucket `architravetestdb-dns-test-2025` contains files in public/  
✅ Pod can access `/storage/clear/public/index.php`  
✅ Database configuration is loaded from Secrets Manager  
✅ Web application responds with 200 OK  

## Next Steps

After successful deployment:

1. **Configure DNS**: Point your domain to the ALB
2. **SSL Certificate**: Ensure SSL certificate covers your domain
3. **Queue Pods**: Fix the CrashLoopBackOff queue pods
4. **Monitoring**: Set up monitoring for the tenant
5. **Backup**: Configure backup for tenant data

## Support

If issues persist:

1. Run the verification script: `./verify-dns-test-2025-fix.sh`
2. Check the troubleshooting section above
3. Review pod logs and events
4. Verify AWS IAM permissions are correctly applied