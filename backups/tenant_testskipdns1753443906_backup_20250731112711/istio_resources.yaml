apiVersion: v1
items:
- apiVersion: networking.istio.io/v1
  kind: VirtualService
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"networking.istio.io/v1beta1","kind":"VirtualService","metadata":{"annotations":{},"labels":{"managed-by":"tenant-onboarding","tenant":"testskipdns1753443906"},"name":"tenant-testskipdns1753443906-vs","namespace":"tenant-testskipdns1753443906"},"spec":{"gateways":["istio-system/tenant-gateway"],"hosts":["testskipdns1753443906.architrave-assets.de"],"http":[{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"match":[{"uri":{"prefix":"/api"}}],"retries":{"attempts":3,"perTryTimeout":"10s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"testskipdns1753443906-backend-service","port":{"number":8080}}}],"timeout":"30s"},{"headers":{"request":{"set":{"X-Forwarded-Proto":"https"}}},"retries":{"attempts":2,"perTryTimeout":"5s","retryOn":"gateway-error,connect-failure,refused-stream"},"route":[{"destination":{"host":"testskipdns1753443906-frontend-service","port":{"number":80}}}],"timeout":"15s"}]}}
    creationTimestamp: "2025-07-25T11:51:03Z"
    generation: 1
    labels:
      managed-by: tenant-onboarding
      tenant: testskipdns1753443906
    name: tenant-testskipdns1753443906-vs
    namespace: tenant-testskipdns1753443906
    resourceVersion: "5523803"
    uid: c2f5f72b-f37b-4486-855e-ef0b35edf7de
  spec:
    gateways:
    - istio-system/tenant-gateway
    hosts:
    - testskipdns1753443906.architrave-assets.de
    http:
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      match:
      - uri:
          prefix: /api
      retries:
        attempts: 3
        perTryTimeout: 10s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: testskipdns1753443906-backend-service
          port:
            number: 8080
      timeout: 30s
    - headers:
        request:
          set:
            X-Forwarded-Proto: https
      retries:
        attempts: 2
        perTryTimeout: 5s
        retryOn: gateway-error,connect-failure,refused-stream
      route:
      - destination:
          host: testskipdns1753443906-frontend-service
          port:
            number: 80
      timeout: 15s
kind: List
metadata:
  resourceVersion: ""
