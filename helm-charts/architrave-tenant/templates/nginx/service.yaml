{{- if .Values.nginx.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.tenant.id }}-nginx
  namespace: {{ .Values.tenant.namespace }}
  labels:
    app: {{ .Values.tenant.id }}-nginx
    app.kubernetes.io/name: {{ include "architrave-tenant.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    component: nginx
  annotations:
    owner: architrave
spec:
  type: {{ .Values.nginx.service.type | default "ClusterIP" }}
  ports:
    - port: 443
      targetPort: 8080
      protocol: TCP
      name: https
  selector:
    app: webapp
    component: webapp
{{- end }}
