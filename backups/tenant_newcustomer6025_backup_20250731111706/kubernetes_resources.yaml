apiVersion: v1
items:
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-30T14:42:53Z"
    generateName: newcustomer6025-backend-86d9c78dc7-
    labels:
      app: newcustomer6025-backend
      component: backend
      pod-template-hash: 86d9c78dc7
      tenant: newcustomer6025
    name: newcustomer6025-backend-86d9c78dc7-45fs2
    namespace: tenant-newcustomer6025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: newcustomer6025-backend-86d9c78dc7
      uid: 2faffc4b-4d67-4bbb-99a5-f1e432059238
    resourceVersion: "8600474"
    uid: a3357734-6c33-4611-9dea-481ac995aeef
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: newcustomer6025
      - name: DB_HOST
        valueFrom:
          secretKeyRef:
            key: DB_HOST
            name: newcustomer6025-db-secret
      - name: DB_PORT
        valueFrom:
          secretKeyRef:
            key: DB_PORT
            name: newcustomer6025-db-secret
      - name: DB_NAME
        valueFrom:
          secretKeyRef:
            key: DB_NAME
            name: newcustomer6025-db-secret
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: newcustomer6025-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: newcustomer6025-db-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: newcustomer6025-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: newcustomer6025-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: newcustomer6025-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@production-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: en
      - name: LANGUAGE
        value: newcustomer6025
      - name: COGNITO_USER_POOL_ID
        value: test-pool
      - name: COGNITO_CLIENT_ID
        value: test-client
      - name: COGNITO_CLIENT_SECRET
        value: test-secret
      - name: COGNITO_REGION
        value: eu-central-1
      - name: COGNITO_DOMAIN
        value: test-domain
      - name: RABBITMQ_HOST
        value: newcustomer6025-rabbitmq-service
      - name: RABBITMQ_USER
        value: guest
      - name: RABBITMQ_PASSWORD
        value: guest
      image: newcustomer6025
      imagePullPolicy: Always
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
    - image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating local.php configuration with SSL, Cognito, directory, RabbitMQ, and encryption settings..."
        mkdir -p /shared-app/config/autoload
        cat > /shared-app/config/autoload/local.php << 'EOF'
        <?php
        $config = [
            'doctrine' => [
                'connection' => [
                    'orm_default' => [
                        'driverClass' => 'Doctrine\DBAL\Driver\PDOMySql\Driver',
                        'params' => [
                            'host' => getenv('DB_HOST'),
                            'port' => getenv('DB_PORT'),
                            'user' => getenv('DB_USER'),
                            'password' => getenv('DB_PASSWORD'),
                            'dbname' => getenv('DB_NAME'),
                            'charset' => 'utf8mb4',
                            'driverOptions' => [
                                PDO::MYSQL_ATTR_SSL_CA => '/tmp/rds-ca-2019-root.pem',
                                PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                            ],
                        ],
                    ],
                ],
            ],
            'cloud_communication' => [
                'ipro' => [
                    'cognito' => [
                        'user_pool_id' => getenv('COGNITO_USER_POOL_ID') ?: 'test-pool',
                        'client_id' => getenv('COGNITO_CLIENT_ID') ?: 'test-client',
                        'client_secret' => getenv('COGNITO_CLIENT_SECRET') ?: 'test-secret',
                        'region' => getenv('COGNITO_REGION') ?: 'eu-central-1',
                        'domain' => getenv('COGNITO_DOMAIN') ?: 'test-domain',
                    ],
                ],
            ],
            'dir' => [
                'assets' => '/shared-app/data/assets',
                'tmp' => '/tmp',
                'quarantine' => '/shared-app/data/quarantine',
            ],
            'rabbitmq' => [
                'host' => getenv('RABBITMQ_HOST') ?: '************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24-rabbitmq-service',
                'port' => 5672,
                'username' => getenv('RABBITMQ_USER') ?: 'guest',
                'password' => getenv('RABBITMQ_PASSWORD') ?: 'guest',
                'vhost' => '/',
            ],
        ];

        // Add encryption key configuration
        $config['instance_pre_shared_key'] = 'tenant-************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24-key';

        // Add Doctrine cache configuration
        $config['doctrine'] = array_merge_recursive(
            $config['doctrine'],
            [
                'cache' => [
                    'class' => 'Doctrine\Common\Cache\ApcuCache',
                ],
                'configuration' => [
                    'orm_default' => [
                        'metadata_cache' => 'apcu',
                        'query_cache' => 'apcu',
                        'result_cache' => 'apcu',
                        'generate_proxies' => false,
                    ],
                ],
            ]
        );

        return $config;
        EOF
        echo "✅ local.php configuration created successfully"

        # Create required directories with proper permissions
        echo "Creating required directories..."
        mkdir -p /shared-app/data/assets
        mkdir -p /shared-app/data/quarantine
        chmod 755 /shared-app/data/assets
        chmod 755 /shared-app/data/quarantine
        echo "✅ Required directories created successfully"
      image: busybox:latest
      imagePullPolicy: Always
      name: local-php-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
    nodeName: ip-10-0-10-209.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-mvkz5
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:42:55Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:43:06Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:42:53Z"
      message: 'containers with unready status: [backend]'
      reason: ContainersNotReady
      status: "False"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:42:53Z"
      message: 'containers with unready status: [backend]'
      reason: ContainersNotReady
      status: "False"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T14:42:53Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - image: newcustomer6025
      imageID: ""
      lastState: {}
      name: backend
      ready: false
      restartCount: 0
      started: false
      state:
        waiting:
          message: 'Back-off pulling image "newcustomer6025": ErrImagePull: failed
            to pull and unpack image "docker.io/library/newcustomer6025:latest": failed
            to resolve reference "docker.io/library/newcustomer6025:latest": pull
            access denied, repository does not exist or may require authorization:
            server message: insufficient_scope: authorization failed'
          reason: ImagePullBackOff
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://10208d49b9989753bc15fd384961f82397f1c0599733caa8ebf22abb3c4fc3dd
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T14:43:06Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://ec36d1b059b62413c6fc60f04277a835e3ea0faefde218d7517801e818ccac53
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://ec36d1b059b62413c6fc60f04277a835e3ea0faefde218d7517801e818ccac53
          exitCode: 0
          finishedAt: "2025-07-30T14:42:54Z"
          reason: Completed
          startedAt: "2025-07-30T14:42:54Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://633b515b5df4d80c95e739e7caa382321c558b1f14f7f65e3161ee04bce6a2c4
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://633b515b5df4d80c95e739e7caa382321c558b1f14f7f65e3161ee04bce6a2c4
          exitCode: 0
          finishedAt: "2025-07-30T14:42:59Z"
          reason: Completed
          startedAt: "2025-07-30T14:42:56Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://f4396c5c164601cfa3fe04f0f1b2e27a4872e7041f0aba24b89a9e072e2376e7
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://f4396c5c164601cfa3fe04f0f1b2e27a4872e7041f0aba24b89a9e072e2376e7
          exitCode: 0
          finishedAt: "2025-07-30T14:43:02Z"
          reason: Completed
          startedAt: "2025-07-30T14:43:02Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://d9e79aa2e07a71bdbd474c2b938cba223aee2ee7643966f902065a1ea7c18581
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:a1dde0be7914dc18db37c6d1a4a1f6e462fe347ec6643fb5253a1bfe2544ef6b
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://d9e79aa2e07a71bdbd474c2b938cba223aee2ee7643966f902065a1ea7c18581
          exitCode: 0
          finishedAt: "2025-07-30T14:43:03Z"
          reason: Completed
          startedAt: "2025-07-30T14:43:03Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://9cb71e454f423555a9db08aa31fbba198a6e63b3e8bb7355caa702ec3116030c
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: local-php-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://9cb71e454f423555a9db08aa31fbba198a6e63b3e8bb7355caa702ec3116030c
          exitCode: 0
          finishedAt: "2025-07-30T14:43:04Z"
          reason: Completed
          startedAt: "2025-07-30T14:43:04Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://451897ceebc4d968c2247b6b64952099b0348840e877b414b047a4342e91d5c0
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://451897ceebc4d968c2247b6b64952099b0348840e877b414b047a4342e91d5c0
          exitCode: 0
          finishedAt: "2025-07-30T14:43:05Z"
          reason: Completed
          startedAt: "2025-07-30T14:43:05Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mvkz5
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Pending
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-30T14:42:53Z"
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newcustomer6025-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"newcustomer6025"},"name":"newcustomer6025-rabbitmq-mgmt-service","namespace":"tenant-newcustomer6025"},"spec":{"ports":[{"port":15672,"protocol":"TCP","targetPort":15672}],"selector":{"app":"newcustomer6025-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"newcustomer6025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T14:26:28Z"
    labels:
      app: newcustomer6025-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: newcustomer6025
    name: newcustomer6025-rabbitmq-mgmt-service
    namespace: tenant-newcustomer6025
    resourceVersion: "8183528"
    uid: afae67e9-1ab0-4b2d-8ee8-7defeea76fe5
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 15672
      protocol: TCP
      targetPort: 15672
    selector:
      app: newcustomer6025-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: newcustomer6025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"newcustomer6025-rabbitmq","component":"rabbitmq","tenant":"newcustomer6025"},"name":"newcustomer6025-rabbitmq-service","namespace":"tenant-newcustomer6025"},"spec":{"ports":[{"port":5672,"protocol":"TCP","targetPort":5672}],"selector":{"app":"newcustomer6025-rabbitmq","component":"rabbitmq","tenant":"newcustomer6025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-30T14:26:27Z"
    labels:
      app: newcustomer6025-rabbitmq
      component: rabbitmq
      tenant: newcustomer6025
    name: newcustomer6025-rabbitmq-service
    namespace: tenant-newcustomer6025
    resourceVersion: "8183514"
    uid: 0e7b1ea8-511b-4128-a198-d898fe124a4c
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 5672
      protocol: TCP
      targetPort: 5672
    selector:
      app: newcustomer6025-rabbitmq
      component: rabbitmq
      tenant: newcustomer6025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "1"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"newcustomer6025-backend","component":"backend","tenant":"newcustomer6025"},"name":"newcustomer6025-backend","namespace":"tenant-newcustomer6025"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"newcustomer6025-backend","component":"backend","tenant":"newcustomer6025"}},"template":{"metadata":{"labels":{"app":"newcustomer6025-backend","component":"backend","tenant":"newcustomer6025"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"newcustomer6025"},{"name":"DB_HOST","valueFrom":{"secretKeyRef":{"key":"DB_HOST","name":"newcustomer6025-db-secret"}}},{"name":"DB_PORT","valueFrom":{"secretKeyRef":{"key":"DB_PORT","name":"newcustomer6025-db-secret"}}},{"name":"DB_NAME","valueFrom":{"secretKeyRef":{"key":"DB_NAME","name":"newcustomer6025-db-secret"}}},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"DB_USER","name":"newcustomer6025-db-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"DB_PASSWORD","name":"newcustomer6025-db-secret"}}},{"name":"DB_SSL","valueFrom":{"secretKeyRef":{"key":"DB_SSL","name":"newcustomer6025-db-secret"}}},{"name":"DB_SSL_CA","valueFrom":{"secretKeyRef":{"key":"DB_SSL_CA","name":"newcustomer6025-db-secret"}}},{"name":"DB_SSL_VERIFY","valueFrom":{"secretKeyRef":{"key":"DB_SSL_VERIFY","name":"newcustomer6025-db-secret"}}},{"name":"RABBITMQ_URL","value":"amqp://guest:guest@production-rabbitmq-service:5672/"},{"name":"ENVIRONMENT","value":"en"},{"name":"LANGUAGE","value":"newcustomer6025"},{"name":"COGNITO_USER_POOL_ID","value":"test-pool"},{"name":"COGNITO_CLIENT_ID","value":"test-client"},{"name":"COGNITO_CLIENT_SECRET","value":"test-secret"},{"name":"COGNITO_REGION","value":"eu-central-1"},{"name":"COGNITO_DOMAIN","value":"test-domain"},{"name":"RABBITMQ_HOST","value":"newcustomer6025-rabbitmq-service"},{"name":"RABBITMQ_USER","value":"guest"},{"name":"RABBITMQ_PASSWORD","value":"guest"}],"image":"newcustomer6025","livenessProbe":{"initialDelaySeconds":60,"periodSeconds":30,"tcpSocket":{"port":9000}},"name":"backend","ports":[{"containerPort":9000}],"readinessProbe":{"initialDelaySeconds":30,"periodSeconds":30,"tcpSocket":{"port":9000}},"resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}},"volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"},{"mountPath":"/shared-app","name":"shared-app"}]},{"image":"nginx:1.21-alpine","livenessProbe":{"failureThreshold":5,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-liveness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":90,"periodSeconds":60,"successThreshold":1,"timeoutSeconds":10},"name":"nginx","ports":[{"containerPort":8080}],"readinessProbe":{"failureThreshold":10,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-readiness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"timeoutSeconds":5},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"},{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Downloading RDS CA certificate...\"\ncurl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem\necho \"SSL certificate downloaded successfully\"\n"],"image":"curlimages/curl:latest","name":"ssl-cert-downloader","volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"}]},{"command":["sh","-c","echo \"Copying application files from /storage/ArchAssets to shared volume...\"\nif [ -d \"/storage/ArchAssets\" ]; then\n    cp -r /storage/ArchAssets/* /shared-app/ 2\u003e/dev/null || true\n    echo \"Application files copied successfully from /storage/ArchAssets\"\n    echo \"Copied files:\"\n    ls -la /shared-app/ | head -10\nelse\n    echo \"Warning: /storage/ArchAssets directory not found in webapp_dev image\"\n    echo \"Available directories in /storage:\"\n    ls -la /storage/ 2\u003e/dev/null || echo \"No /storage directory\"\n    echo \"Available directories in root:\"\n    ls -la / | grep -E \"(var|storage|app)\" || echo \"No relevant directories found\"\nfi\necho \"Application files copy process completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"app-files-copier","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Fixing PHP application config path issues and PHP-FPM compatibility...\"\n\n# Fix the relative path issue in /shared-app/public/api/index.php\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Found API index.php, fixing config path...\"\n\n  # Create backup\n  cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup\n\n  # Fix the relative path issue by using absolute path\n  sed -i \"s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g\" /shared-app/public/api/index.php\n\n  echo \"Config path fixed in API index.php\"\n  echo \"Checking the fix:\"\n  grep -n \"require.*config.*application.config.php\" /shared-app/public/api/index.php || echo \"Pattern not found after fix\"\nelse\n  echo \"Warning: /shared-app/public/api/index.php not found\"\n  echo \"Available files in /shared-app/public/:\"\n  ls -la /shared-app/public/ 2\u003e/dev/null || echo \"No public directory\"\nfi\n\n# Create PHP-FPM compatibility polyfill for apache_request_headers()\necho \"Creating PHP-FPM compatibility polyfill...\"\ncat \u003e /shared-app/php_fpm_polyfill.php \u003c\u003c 'POLYFILL_EOF'\n\u003c?php\n/**\n * PHP-FPM Compatibility Polyfill for Apache functions\n * This file provides missing Apache functions for PHP-FPM + nginx environments\n *\n * NOTE: Using forced function definition without conditional checks\n * because function_exists() may return false positives in some PHP-FPM environments\n */\n\n/**\n * Polyfill for apache_request_headers() function in PHP-FPM environment\n * Only define if the function doesn't already exist\n */\nif (!function_exists('apache_request_headers')) {\n    function apache_request_headers() {\n        $headers = array();\n\n        // Get all HTTP headers from $_SERVER superglobal\n        foreach ($_SERVER as $key =\u003e $value) {\n            if (strpos($key, 'HTTP_') === 0) {\n                // Convert HTTP_HEADER_NAME to Header-Name format\n                $header = str_replace('_', '-', substr($key, 5));\n                $header = ucwords(strtolower($header), '-');\n                $headers[$header] = $value;\n            }\n        }\n\n        // Add special headers that might not have HTTP_ prefix\n        if (isset($_SERVER['CONTENT_TYPE'])) {\n            $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];\n        }\n        if (isset($_SERVER['CONTENT_LENGTH'])) {\n            $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];\n        }\n\n        return $headers;\n    }\n}\n\n/**\n * Polyfill for apache_response_headers() function in PHP-FPM environment\n * @return array\n */\nfunction apache_response_headers() {\n    $headers = array();\n\n    // Get headers that were set with header() function\n    if (function_exists('headers_list')) {\n        foreach (headers_list() as $header) {\n            $parts = explode(':', $header, 2);\n            if (count($parts) === 2) {\n                $headers[trim($parts[0])] = trim($parts[1]);\n            }\n        }\n    }\n\n    return $headers;\n}\n\n/**\n * Alias for apache_request_headers() - commonly used alternative\n * Only define if the function doesn't already exist\n */\nif (!function_exists('getallheaders')) {\n    function getallheaders() {\n        return apache_request_headers();\n    }\n}\nPOLYFILL_EOF\n\necho \"✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php\"\n\n# Inject the polyfill into the main bootstrap file\nif [ -f \"/shared-app/bootstrap.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into bootstrap.php...\"\n\n  # Create backup\n  cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bootstrap.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into bootstrap.php\"\nelse\n  echo \"Warning: /shared-app/bootstrap.php not found\"\nfi\n\n# Also inject into the API index.php as a fallback\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into API index.php as fallback...\"\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/public/api/index.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into API index.php\"\nfi\n\n# CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)...\"\n\n  # Create backup\n  cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/architrave.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into CLI entry point\"\nelse\n  echo \"Warning: /shared-app/bin/architrave.php not found\"\nfi\n\n# Also check for alternative CLI entry points\nif [ -f \"/shared-app/bin/console.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into console.php...\"\n\n  # Create backup\n  cp /shared-app/bin/console.php /shared-app/bin/console.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/console.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into console.php\"\nfi\n\n# Verify config file exists\nif [ -f \"/shared-app/config/application.config.php\" ]; then\n  echo \"✅ Config file exists at /shared-app/config/application.config.php\"\nelse\n  echo \"❌ Config file missing at /shared-app/config/application.config.php\"\n  echo \"Available files in /shared-app/config/:\"\n  ls -la /shared-app/config/ 2\u003e/dev/null || echo \"No config directory\"\nfi\n\necho \"✅ PHP config path fix and PHP-FPM compatibility setup completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-config-fixer","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Validating PHP environment for PHP-FPM + nginx compatibility...\"\n\n# Test if polyfill was loaded correctly\nphp -r \"\nrequire_once '/shared-app/php_fpm_polyfill.php';\n\necho 'Testing PHP-FPM polyfill functions...\\n';\n\n// Test apache_request_headers function\nif (function_exists('apache_request_headers')) {\n    echo '✅ apache_request_headers() function is available\\n';\n} else {\n    echo '❌ apache_request_headers() function is missing\\n';\n    exit(1);\n}\n\n// Test getallheaders function\nif (function_exists('getallheaders')) {\n    echo '✅ getallheaders() function is available\\n';\n} else {\n    echo '❌ getallheaders() function is missing\\n';\n    exit(1);\n}\n\n// Test apache_response_headers function\nif (function_exists('apache_response_headers')) {\n    echo '✅ apache_response_headers() function is available\\n';\n} else {\n    echo '❌ apache_response_headers() function is missing\\n';\n    exit(1);\n}\n\necho '✅ All PHP-FPM polyfill functions are working correctly\\n';\n\"\n\n# Test basic PHP application loading\necho \"Testing PHP application bootstrap loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    echo '✅ Bootstrap loaded successfully\\n';\n} catch (Exception \\$e) {\n    echo '❌ Bootstrap loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# Test Laminas application config loading\necho \"Testing Laminas application config loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    \\$config = require '/shared-app/config/application.config.php';\n    echo '✅ Application config loaded successfully\\n';\n    echo 'Config modules: ' . count(\\$config['modules']) . '\\n';\n} catch (Exception \\$e) {\n    echo '❌ Application config loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# CRITICAL FIX: Test CLI entry point functionality\necho \"Testing CLI entry point functionality...\"\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Testing bin/architrave.php polyfill integration...\"\n  php -d display_errors=1 -d error_reporting=E_ALL -r \"\n  try {\n      // Change to the correct directory\n      chdir('/shared-app');\n\n      // Test if polyfill file exists and is readable\n      if (!file_exists('php_fpm_polyfill.php')) {\n          echo '❌ Polyfill file not found\\n';\n          exit(1);\n      }\n\n      // Load polyfill manually to test\n      require_once 'php_fpm_polyfill.php';\n\n      // Test if functions are available\n      if (function_exists('apache_request_headers')) {\n          echo '✅ CLI polyfill functions available\\n';\n      } else {\n          echo '❌ CLI polyfill functions missing\\n';\n          exit(1);\n      }\n\n      echo '✅ CLI environment validation successful\\n';\n  } catch (Exception \\$e) {\n      echo '⚠️ CLI test failed: ' . \\$e-\u003egetMessage() . '\\n';\n      echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n      echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n      // Don't exit with error as this might fail in init container context\n  }\n  \" || echo \"⚠️ CLI test completed with warnings (normal in init container)\"\nelse\n  echo \"⚠️ CLI entry point not found - will be available after application sync\"\nfi\n\necho \"✅ PHP environment validation completed successfully\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-env-validator","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Creating local.php configuration with SSL, Cognito, directory, RabbitMQ, and encryption settings...\"\nmkdir -p /shared-app/config/autoload\ncat \u003e /shared-app/config/autoload/local.php \u003c\u003c 'EOF'\n\u003c?php\n$config = [\n    'doctrine' =\u003e [\n        'connection' =\u003e [\n            'orm_default' =\u003e [\n                'driverClass' =\u003e 'Doctrine\\DBAL\\Driver\\PDOMySql\\Driver',\n                'params' =\u003e [\n                    'host' =\u003e getenv('DB_HOST'),\n                    'port' =\u003e getenv('DB_PORT'),\n                    'user' =\u003e getenv('DB_USER'),\n                    'password' =\u003e getenv('DB_PASSWORD'),\n                    'dbname' =\u003e getenv('DB_NAME'),\n                    'charset' =\u003e 'utf8mb4',\n                    'driverOptions' =\u003e [\n                        PDO::MYSQL_ATTR_SSL_CA =\u003e '/tmp/rds-ca-2019-root.pem',\n                        PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT =\u003e false,\n                    ],\n                ],\n            ],\n        ],\n    ],\n    'cloud_communication' =\u003e [\n        'ipro' =\u003e [\n            'cognito' =\u003e [\n                'user_pool_id' =\u003e getenv('COGNITO_USER_POOL_ID') ?: 'test-pool',\n                'client_id' =\u003e getenv('COGNITO_CLIENT_ID') ?: 'test-client',\n                'client_secret' =\u003e getenv('COGNITO_CLIENT_SECRET') ?: 'test-secret',\n                'region' =\u003e getenv('COGNITO_REGION') ?: 'eu-central-1',\n                'domain' =\u003e getenv('COGNITO_DOMAIN') ?: 'test-domain',\n            ],\n        ],\n    ],\n    'dir' =\u003e [\n        'assets' =\u003e '/shared-app/data/assets',\n        'tmp' =\u003e '/tmp',\n        'quarantine' =\u003e '/shared-app/data/quarantine',\n    ],\n    'rabbitmq' =\u003e [\n        'host' =\u003e getenv('RABBITMQ_HOST') ?: '************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24-rabbitmq-service',\n        'port' =\u003e 5672,\n        'username' =\u003e getenv('RABBITMQ_USER') ?: 'guest',\n        'password' =\u003e getenv('RABBITMQ_PASSWORD') ?: 'guest',\n        'vhost' =\u003e '/',\n    ],\n];\n\n// Add encryption key configuration\n$config['instance_pre_shared_key'] = 'tenant-************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24-key';\n\n// Add Doctrine cache configuration\n$config['doctrine'] = array_merge_recursive(\n    $config['doctrine'],\n    [\n        'cache' =\u003e [\n            'class' =\u003e 'Doctrine\\Common\\Cache\\ApcuCache',\n        ],\n        'configuration' =\u003e [\n            'orm_default' =\u003e [\n                'metadata_cache' =\u003e 'apcu',\n                'query_cache' =\u003e 'apcu',\n                'result_cache' =\u003e 'apcu',\n                'generate_proxies' =\u003e false,\n            ],\n        ],\n    ]\n);\n\nreturn $config;\nEOF\necho \"✅ local.php configuration created successfully\"\n\n# Create required directories with proper permissions\necho \"Creating required directories...\"\nmkdir -p /shared-app/data/assets\nmkdir -p /shared-app/data/quarantine\nchmod 755 /shared-app/data/assets\nchmod 755 /shared-app/data/quarantine\necho \"✅ Required directories created successfully\"\n"],"image":"busybox:latest","name":"local-php-config-setup","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Creating HTTP nginx configuration for port 8080...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 8080;\n    server_name localhost;\n    root /shared-app/public;\n    index index.php index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /api/health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # Root endpoint - redirect to API\n    location = / {\n        return 302 /api/;\n    }\n\n    location / {\n        try_files $uri $uri/ /api/index.php?$args;\n    }\n\n    location ~ \\.php$ {\n        fastcgi_pass localhost:9000;\n        fastcgi_index index.php;\n        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\n        fastcgi_param AUTOMATED_TEST 1;\n        include fastcgi_params;\n        fastcgi_read_timeout 310;\n    }\n\n    location ~ /\\.ht {\n        deny all;\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"ssl-certs"},{"emptyDir":{},"name":"shared-app"},{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-30T14:42:53Z"
    generation: 1
    labels:
      app: newcustomer6025-backend
      component: backend
      tenant: newcustomer6025
    name: newcustomer6025-backend
    namespace: tenant-newcustomer6025
    resourceVersion: "8193530"
    uid: a6b26c53-48a0-466e-8eb5-270e6ea313c2
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: newcustomer6025-backend
        component: backend
        tenant: newcustomer6025
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newcustomer6025-backend
          component: backend
          tenant: newcustomer6025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newcustomer6025
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newcustomer6025-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newcustomer6025-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newcustomer6025-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newcustomer6025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newcustomer6025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newcustomer6025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newcustomer6025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newcustomer6025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@production-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: en
          - name: LANGUAGE
            value: newcustomer6025
          - name: COGNITO_USER_POOL_ID
            value: test-pool
          - name: COGNITO_CLIENT_ID
            value: test-client
          - name: COGNITO_CLIENT_SECRET
            value: test-secret
          - name: COGNITO_REGION
            value: eu-central-1
          - name: COGNITO_DOMAIN
            value: test-domain
          - name: RABBITMQ_HOST
            value: newcustomer6025-rabbitmq-service
          - name: RABBITMQ_USER
            value: guest
          - name: RABBITMQ_PASSWORD
            value: guest
          image: newcustomer6025
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating local.php configuration with SSL, Cognito, directory, RabbitMQ, and encryption settings..."
            mkdir -p /shared-app/config/autoload
            cat > /shared-app/config/autoload/local.php << 'EOF'
            <?php
            $config = [
                'doctrine' => [
                    'connection' => [
                        'orm_default' => [
                            'driverClass' => 'Doctrine\DBAL\Driver\PDOMySql\Driver',
                            'params' => [
                                'host' => getenv('DB_HOST'),
                                'port' => getenv('DB_PORT'),
                                'user' => getenv('DB_USER'),
                                'password' => getenv('DB_PASSWORD'),
                                'dbname' => getenv('DB_NAME'),
                                'charset' => 'utf8mb4',
                                'driverOptions' => [
                                    PDO::MYSQL_ATTR_SSL_CA => '/tmp/rds-ca-2019-root.pem',
                                    PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                                ],
                            ],
                        ],
                    ],
                ],
                'cloud_communication' => [
                    'ipro' => [
                        'cognito' => [
                            'user_pool_id' => getenv('COGNITO_USER_POOL_ID') ?: 'test-pool',
                            'client_id' => getenv('COGNITO_CLIENT_ID') ?: 'test-client',
                            'client_secret' => getenv('COGNITO_CLIENT_SECRET') ?: 'test-secret',
                            'region' => getenv('COGNITO_REGION') ?: 'eu-central-1',
                            'domain' => getenv('COGNITO_DOMAIN') ?: 'test-domain',
                        ],
                    ],
                ],
                'dir' => [
                    'assets' => '/shared-app/data/assets',
                    'tmp' => '/tmp',
                    'quarantine' => '/shared-app/data/quarantine',
                ],
                'rabbitmq' => [
                    'host' => getenv('RABBITMQ_HOST') ?: '************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24-rabbitmq-service',
                    'port' => 5672,
                    'username' => getenv('RABBITMQ_USER') ?: 'guest',
                    'password' => getenv('RABBITMQ_PASSWORD') ?: 'guest',
                    'vhost' => '/',
                ],
            ];

            // Add encryption key configuration
            $config['instance_pre_shared_key'] = 'tenant-************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24-key';

            // Add Doctrine cache configuration
            $config['doctrine'] = array_merge_recursive(
                $config['doctrine'],
                [
                    'cache' => [
                        'class' => 'Doctrine\Common\Cache\ApcuCache',
                    ],
                    'configuration' => [
                        'orm_default' => [
                            'metadata_cache' => 'apcu',
                            'query_cache' => 'apcu',
                            'result_cache' => 'apcu',
                            'generate_proxies' => false,
                        ],
                    ],
                ]
            );

            return $config;
            EOF
            echo "✅ local.php configuration created successfully"

            # Create required directories with proper permissions
            echo "Creating required directories..."
            mkdir -p /shared-app/data/assets
            mkdir -p /shared-app/data/quarantine
            chmod 755 /shared-app/data/assets
            chmod 755 /shared-app/data/quarantine
            echo "✅ Required directories created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: local-php-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    conditions:
    - lastTransitionTime: "2025-07-30T14:42:53Z"
      lastUpdateTime: "2025-07-30T14:42:53Z"
      message: Deployment does not have minimum availability.
      reason: MinimumReplicasUnavailable
      status: "False"
      type: Available
    - lastTransitionTime: "2025-07-30T14:52:54Z"
      lastUpdateTime: "2025-07-30T14:52:54Z"
      message: ReplicaSet "newcustomer6025-backend-86d9c78dc7" has timed out progressing.
      reason: ProgressDeadlineExceeded
      status: "False"
      type: Progressing
    observedGeneration: 1
    replicas: 1
    unavailableReplicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-30T14:42:53Z"
    generation: 1
    labels:
      app: newcustomer6025-backend
      component: backend
      pod-template-hash: 86d9c78dc7
      tenant: newcustomer6025
    name: newcustomer6025-backend-86d9c78dc7
    namespace: tenant-newcustomer6025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: newcustomer6025-backend
      uid: a6b26c53-48a0-466e-8eb5-270e6ea313c2
    resourceVersion: "8189816"
    uid: 2faffc4b-4d67-4bbb-99a5-f1e432059238
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: newcustomer6025-backend
        component: backend
        pod-template-hash: 86d9c78dc7
        tenant: newcustomer6025
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: newcustomer6025-backend
          component: backend
          pod-template-hash: 86d9c78dc7
          tenant: newcustomer6025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: newcustomer6025
          - name: DB_HOST
            valueFrom:
              secretKeyRef:
                key: DB_HOST
                name: newcustomer6025-db-secret
          - name: DB_PORT
            valueFrom:
              secretKeyRef:
                key: DB_PORT
                name: newcustomer6025-db-secret
          - name: DB_NAME
            valueFrom:
              secretKeyRef:
                key: DB_NAME
                name: newcustomer6025-db-secret
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: newcustomer6025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: newcustomer6025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: newcustomer6025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: newcustomer6025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: newcustomer6025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@production-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: en
          - name: LANGUAGE
            value: newcustomer6025
          - name: COGNITO_USER_POOL_ID
            value: test-pool
          - name: COGNITO_CLIENT_ID
            value: test-client
          - name: COGNITO_CLIENT_SECRET
            value: test-secret
          - name: COGNITO_REGION
            value: eu-central-1
          - name: COGNITO_DOMAIN
            value: test-domain
          - name: RABBITMQ_HOST
            value: newcustomer6025-rabbitmq-service
          - name: RABBITMQ_USER
            value: guest
          - name: RABBITMQ_PASSWORD
            value: guest
          image: newcustomer6025
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating local.php configuration with SSL, Cognito, directory, RabbitMQ, and encryption settings..."
            mkdir -p /shared-app/config/autoload
            cat > /shared-app/config/autoload/local.php << 'EOF'
            <?php
            $config = [
                'doctrine' => [
                    'connection' => [
                        'orm_default' => [
                            'driverClass' => 'Doctrine\DBAL\Driver\PDOMySql\Driver',
                            'params' => [
                                'host' => getenv('DB_HOST'),
                                'port' => getenv('DB_PORT'),
                                'user' => getenv('DB_USER'),
                                'password' => getenv('DB_PASSWORD'),
                                'dbname' => getenv('DB_NAME'),
                                'charset' => 'utf8mb4',
                                'driverOptions' => [
                                    PDO::MYSQL_ATTR_SSL_CA => '/tmp/rds-ca-2019-root.pem',
                                    PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                                ],
                            ],
                        ],
                    ],
                ],
                'cloud_communication' => [
                    'ipro' => [
                        'cognito' => [
                            'user_pool_id' => getenv('COGNITO_USER_POOL_ID') ?: 'test-pool',
                            'client_id' => getenv('COGNITO_CLIENT_ID') ?: 'test-client',
                            'client_secret' => getenv('COGNITO_CLIENT_SECRET') ?: 'test-secret',
                            'region' => getenv('COGNITO_REGION') ?: 'eu-central-1',
                            'domain' => getenv('COGNITO_DOMAIN') ?: 'test-domain',
                        ],
                    ],
                ],
                'dir' => [
                    'assets' => '/shared-app/data/assets',
                    'tmp' => '/tmp',
                    'quarantine' => '/shared-app/data/quarantine',
                ],
                'rabbitmq' => [
                    'host' => getenv('RABBITMQ_HOST') ?: '************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24-rabbitmq-service',
                    'port' => 5672,
                    'username' => getenv('RABBITMQ_USER') ?: 'guest',
                    'password' => getenv('RABBITMQ_PASSWORD') ?: 'guest',
                    'vhost' => '/',
                ],
            ];

            // Add encryption key configuration
            $config['instance_pre_shared_key'] = 'tenant-************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24-key';

            // Add Doctrine cache configuration
            $config['doctrine'] = array_merge_recursive(
                $config['doctrine'],
                [
                    'cache' => [
                        'class' => 'Doctrine\Common\Cache\ApcuCache',
                    ],
                    'configuration' => [
                        'orm_default' => [
                            'metadata_cache' => 'apcu',
                            'query_cache' => 'apcu',
                            'result_cache' => 'apcu',
                            'generate_proxies' => false,
                        ],
                    ],
                ]
            );

            return $config;
            EOF
            echo "✅ local.php configuration created successfully"

            # Create required directories with proper permissions
            echo "Creating required directories..."
            mkdir -p /shared-app/data/assets
            mkdir -p /shared-app/data/quarantine
            chmod 755 /shared-app/data/assets
            chmod 755 /shared-app/data/quarantine
            echo "✅ Required directories created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: local-php-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    fullyLabeledReplicas: 1
    observedGeneration: 1
    replicas: 1
kind: List
metadata:
  resourceVersion: ""
