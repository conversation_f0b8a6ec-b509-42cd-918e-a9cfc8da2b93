{{- if .Values.webapp.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.tenant.id }}-webapp
  namespace: {{ include "architrave-tenant.namespace" . }}
  labels:
    {{- include "architrave-tenant.labels" . | nindent 4 }}
    app: webapp
    component: webapp
  annotations:
    {{- with .Values.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.webapp.service.type }}
  ports:
    - port: {{ .Values.webapp.service.port }}
      targetPort: {{ .Values.webapp.service.targetPort }}
      protocol: TCP
      name: php-fpm
  selector:
    {{- include "architrave-tenant.selectorLabels" . | nindent 4 }}
    app: webapp
    component: webapp
{{- end }}
