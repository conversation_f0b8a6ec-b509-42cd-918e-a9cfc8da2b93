apiVersion: v1
items:
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-24T09:56:01+02:00"
    creationTimestamp: "2025-07-25T09:05:37Z"
    generateName: prodtest2025-backend-5bf9d5cb74-
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 5bf9d5cb74
      tenant: prodtest2025
    name: prodtest2025-backend-5bf9d5cb74-cddhm
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: prodtest2025-backend-5bf9d5cb74
      uid: ed8bedd2-b44b-4f0c-ba2c-9cefa4990656
    resourceVersion: "5496185"
    uid: 12744494-9a68-4cfb-97cf-9caa0bec8dd6
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: prodtest2025
      - name: DB_HOST
        value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
      - name: DB_PORT
        value: "3306"
      - name: DB_NAME
        value: tenant_prodtest2025
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: prodtest2025-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: prodtest2025-db-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: prodtest2025-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: prodtest2025-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: prodtest2025-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant prodtest2025..."
        aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-12-44.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: prodtest2025-s3-service-account
    serviceAccountName: prodtest2025-s3-service-account
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-vfsfh
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T10:40:30Z"
      message: 'The node was low on resource: ephemeral-storage. Threshold quantity:
        **********, available: 759532Ki. Container backend was using 4Ki, request
        is 0, has larger consumption of ephemeral-storage. Container nginx was using
        8Ki, request is 0, has larger consumption of ephemeral-storage. '
      reason: TerminationByKubelet
      status: "True"
      type: DisruptionTarget
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T10:40:30Z"
      status: "False"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T09:06:05Z"
      reason: PodCompleted
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T10:40:30Z"
      reason: PodCompleted
      status: "False"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T10:40:30Z"
      reason: PodCompleted
      status: "False"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T09:05:37Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://1489ffa974f59ef3bd561644d010ffa205b778230cf217fa1d1509f620cd6f60
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: false
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://1489ffa974f59ef3bd561644d010ffa205b778230cf217fa1d1509f620cd6f60
          exitCode: 0
          finishedAt: "2025-07-25T10:40:27Z"
          reason: Completed
          startedAt: "2025-07-25T09:06:05Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://dbbfa1011f9b7ce5395042735e22a8e89c55fb37b1cebeb62b6e8b3747b85343
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: false
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://dbbfa1011f9b7ce5395042735e22a8e89c55fb37b1cebeb62b6e8b3747b85343
          exitCode: 0
          finishedAt: "2025-07-25T10:40:27Z"
          reason: Completed
          startedAt: "2025-07-25T09:06:05Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: **********
    hostIPs:
    - ip: **********
    initContainerStatuses:
    - containerID: containerd://13ca1931ccf35bc88fa7aa88f3f7f4fea46c29a0d8562f6d0736c46d113eaf2c
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:6ba3a2d743a77d28b13d26383b3ce303b3d6405988245ea5a69d4cb7381565f4
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://13ca1931ccf35bc88fa7aa88f3f7f4fea46c29a0d8562f6d0736c46d113eaf2c
          exitCode: 0
          finishedAt: "2025-07-25T09:05:43Z"
          reason: Completed
          startedAt: "2025-07-25T09:05:39Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://eadc396198b18f0862749c1b6e45ff279236f6c5fcefe7b5d285002cda6a118f
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://eadc396198b18f0862749c1b6e45ff279236f6c5fcefe7b5d285002cda6a118f
          exitCode: 0
          finishedAt: "2025-07-25T09:05:48Z"
          reason: Completed
          startedAt: "2025-07-25T09:05:48Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://85a9f57c21fd8672eb40afd83a41b90456c4d5b4b2084efb2a592072538d69e0
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://85a9f57c21fd8672eb40afd83a41b90456c4d5b4b2084efb2a592072538d69e0
          exitCode: 0
          finishedAt: "2025-07-25T09:05:58Z"
          reason: Completed
          startedAt: "2025-07-25T09:05:49Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://0eb98ee1494320ebff584323539cd801643a075723fa9f4236d6efe8d2c09efd
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://0eb98ee1494320ebff584323539cd801643a075723fa9f4236d6efe8d2c09efd
          exitCode: 0
          finishedAt: "2025-07-25T09:06:01Z"
          reason: Completed
          startedAt: "2025-07-25T09:06:01Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://ebb393aa09d523183fd5ed76961f263b5750ccfb063e1bafb6049962782c9bea
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://ebb393aa09d523183fd5ed76961f263b5750ccfb063e1bafb6049962782c9bea
          exitCode: 0
          finishedAt: "2025-07-25T09:06:02Z"
          reason: Completed
          startedAt: "2025-07-25T09:06:02Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://ed0b0ce5a7938cc977fec8accf355050d7c2cd2ad1a8d5a839af79708f067ec7
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://ed0b0ce5a7938cc977fec8accf355050d7c2cd2ad1a8d5a839af79708f067ec7
          exitCode: 0
          finishedAt: "2025-07-25T09:06:04Z"
          reason: Completed
          startedAt: "2025-07-25T09:06:04Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-vfsfh
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Succeeded
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-25T09:05:37Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-24T09:56:01+02:00"
    creationTimestamp: "2025-07-25T13:35:35Z"
    generateName: prodtest2025-backend-5bf9d5cb74-
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 5bf9d5cb74
      tenant: prodtest2025
    name: prodtest2025-backend-5bf9d5cb74-x5wcq
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: prodtest2025-backend-5bf9d5cb74
      uid: ed8bedd2-b44b-4f0c-ba2c-9cefa4990656
    resourceVersion: "5562243"
    uid: ef000dca-cc97-43e4-ad90-989a1e2e72c5
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: prodtest2025
      - name: DB_HOST
        value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
      - name: DB_PORT
        value: "3306"
      - name: DB_NAME
        value: tenant_prodtest2025
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: prodtest2025-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: prodtest2025-db-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: prodtest2025-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: prodtest2025-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: prodtest2025-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant prodtest2025..."
        aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-12-58.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: prodtest2025-s3-service-account
    serviceAccountName: prodtest2025-s3-service-account
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-cp6x5
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:35:47Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:36:04Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:37:06Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:37:06Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T13:35:35Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://eeb8806218ea4fa1e2693c0e0d8c53552d422cf617410560200adc039050238e
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-25T13:36:04Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://3d12788a9536c15c9a6b23d5f0637b5cc0cec17ab4626e1e8dd3ae83fd8010f2
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-25T13:36:04Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: **********
    hostIPs:
    - ip: **********
    initContainerStatuses:
    - containerID: containerd://dea9f0fed3617d1f09f69d984f91d4670cfd7d30c5c6c8987ccf15b1e1466445
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:6ba3a2d743a77d28b13d26383b3ce303b3d6405988245ea5a69d4cb7381565f4
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://dea9f0fed3617d1f09f69d984f91d4670cfd7d30c5c6c8987ccf15b1e1466445
          exitCode: 0
          finishedAt: "2025-07-25T13:35:48Z"
          reason: Completed
          startedAt: "2025-07-25T13:35:47Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://2c84d0c924135d179a35bad062a1ddc8e385a2f29ecdba5ecc3f35ade2bea03c
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://2c84d0c924135d179a35bad062a1ddc8e385a2f29ecdba5ecc3f35ade2bea03c
          exitCode: 0
          finishedAt: "2025-07-25T13:35:53Z"
          reason: Completed
          startedAt: "2025-07-25T13:35:53Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://0bfe5c0f96432975cec1cb5f11d328733d26de4a4b3eacf259276e53b76da84b
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://0bfe5c0f96432975cec1cb5f11d328733d26de4a4b3eacf259276e53b76da84b
          exitCode: 0
          finishedAt: "2025-07-25T13:35:58Z"
          reason: Completed
          startedAt: "2025-07-25T13:35:54Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://76f9ff5ba601c55fefc04887e1e03674437f29b7e03de7e5ecd4a09c334eee09
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://76f9ff5ba601c55fefc04887e1e03674437f29b7e03de7e5ecd4a09c334eee09
          exitCode: 0
          finishedAt: "2025-07-25T13:36:01Z"
          reason: Completed
          startedAt: "2025-07-25T13:36:01Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://41a75e00afd403e8f870eeec3bf13ab219102b0952ab636acef69cc1785350bb
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://41a75e00afd403e8f870eeec3bf13ab219102b0952ab636acef69cc1785350bb
          exitCode: 0
          finishedAt: "2025-07-25T13:36:03Z"
          reason: Completed
          startedAt: "2025-07-25T13:36:02Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://66d902a1eb75d5e1d1a42f3ff397346740a68e6692364a1a139b2ec10329dc1a
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://66d902a1eb75d5e1d1a42f3ff397346740a68e6692364a1a139b2ec10329dc1a
          exitCode: 0
          finishedAt: "2025-07-25T13:36:04Z"
          reason: Completed
          startedAt: "2025-07-25T13:36:04Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-cp6x5
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-25T13:35:35Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-24T09:56:01+02:00"
    creationTimestamp: "2025-07-30T13:30:36Z"
    generateName: prodtest2025-backend-5bf9d5cb74-
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 5bf9d5cb74
      tenant: prodtest2025
    name: prodtest2025-backend-5bf9d5cb74-zvq84
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: prodtest2025-backend-5bf9d5cb74
      uid: ed8bedd2-b44b-4f0c-ba2c-9cefa4990656
    resourceVersion: "8163340"
    uid: d88ae20e-b04d-443f-8c02-e05397dca0c0
  spec:
    containers:
    - env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: TENANT_ID
        value: prodtest2025
      - name: DB_HOST
        value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
      - name: DB_PORT
        value: "3306"
      - name: DB_NAME
        value: tenant_prodtest2025
      - name: DB_USER
        valueFrom:
          secretKeyRef:
            key: DB_USER
            name: prodtest2025-db-secret
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            key: DB_PASSWORD
            name: prodtest2025-db-secret
      - name: DB_SSL
        valueFrom:
          secretKeyRef:
            key: DB_SSL
            name: prodtest2025-db-secret
      - name: DB_SSL_CA
        valueFrom:
          secretKeyRef:
            key: DB_SSL_CA
            name: prodtest2025-db-secret
      - name: DB_SSL_VERIFY
        valueFrom:
          secretKeyRef:
            key: DB_SSL_VERIFY
            name: prodtest2025-db-secret
      - name: RABBITMQ_URL
        value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      name: backend
      ports:
      - containerPort: 9000
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        tcpSocket:
          port: 9000
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 500m
          memory: 512Mi
        requests:
          cpu: 100m
          memory: 256Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: nginx:1.21-alpine
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 5
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-liveness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 90
        periodSeconds: 60
        successThreshold: 1
        timeoutSeconds: 10
      name: nginx
      ports:
      - containerPort: 8080
        protocol: TCP
      readinessProbe:
        failureThreshold: 10
        httpGet:
          httpHeaders:
          - name: User-Agent
            value: k8s-readiness-probe
          path: /api/health
          port: 8080
          scheme: HTTP
        initialDelaySeconds: 60
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 5
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Syncing S3 content for tenant prodtest2025..."
        aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
        echo "S3 content sync completed"
      env:
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: amazon/aws-cli:latest
      imagePullPolicy: Always
      name: s3-sync
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Downloading RDS CA certificate..."
        curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
        echo "SSL certificate downloaded successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: curlimages/curl:latest
      imagePullPolicy: Always
      name: ssl-cert-downloader
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Copying application files from /storage/ArchAssets to shared volume..."
        if [ -d "/storage/ArchAssets" ]; then
            cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
            echo "Application files copied successfully from /storage/ArchAssets"
            echo "Copied files:"
            ls -la /shared-app/ | head -10
        else
            echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
            echo "Available directories in /storage:"
            ls -la /storage/ 2>/dev/null || echo "No /storage directory"
            echo "Available directories in root:"
            ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
        fi
        echo "Application files copy process completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: app-files-copier
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

        # Fix the relative path issue in /shared-app/public/api/index.php
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Found API index.php, fixing config path..."

          # Create backup
          cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

          # Fix the relative path issue by using absolute path
          sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

          echo "Config path fixed in API index.php"
          echo "Checking the fix:"
          grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
        else
          echo "Warning: /shared-app/public/api/index.php not found"
          echo "Available files in /shared-app/public/:"
          ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
        fi

        # Create PHP-FPM compatibility polyfill for apache_request_headers()
        echo "Creating PHP-FPM compatibility polyfill..."
        cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
        <?php
        /**
         * PHP-FPM Compatibility Polyfill for Apache functions
         * This file provides missing Apache functions for PHP-FPM + nginx environments
         *
         * NOTE: Using forced function definition without conditional checks
         * because function_exists() may return false positives in some PHP-FPM environments
         */

        /**
         * Polyfill for apache_request_headers() function in PHP-FPM environment
         * Only define if the function doesn't already exist
         */
        if (!function_exists('apache_request_headers')) {
            function apache_request_headers() {
                $headers = array();

                // Get all HTTP headers from $_SERVER superglobal
                foreach ($_SERVER as $key => $value) {
                    if (strpos($key, 'HTTP_') === 0) {
                        // Convert HTTP_HEADER_NAME to Header-Name format
                        $header = str_replace('_', '-', substr($key, 5));
                        $header = ucwords(strtolower($header), '-');
                        $headers[$header] = $value;
                    }
                }

                // Add special headers that might not have HTTP_ prefix
                if (isset($_SERVER['CONTENT_TYPE'])) {
                    $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                }
                if (isset($_SERVER['CONTENT_LENGTH'])) {
                    $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                }

                return $headers;
            }
        }

        /**
         * Polyfill for apache_response_headers() function in PHP-FPM environment
         * @return array
         */
        function apache_response_headers() {
            $headers = array();

            // Get headers that were set with header() function
            if (function_exists('headers_list')) {
                foreach (headers_list() as $header) {
                    $parts = explode(':', $header, 2);
                    if (count($parts) === 2) {
                        $headers[trim($parts[0])] = trim($parts[1]);
                    }
                }
            }

            return $headers;
        }

        /**
         * Alias for apache_request_headers() - commonly used alternative
         * Only define if the function doesn't already exist
         */
        if (!function_exists('getallheaders')) {
            function getallheaders() {
                return apache_request_headers();
            }
        }
        POLYFILL_EOF

        echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

        # Inject the polyfill into the main bootstrap file
        if [ -f "/shared-app/bootstrap.php" ]; then
          echo "Injecting PHP-FPM polyfill into bootstrap.php..."

          # Create backup
          cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
          fi

          echo "✅ PHP-FPM polyfill injected into bootstrap.php"
        else
          echo "Warning: /shared-app/bootstrap.php not found"
        fi

        # Also inject into the API index.php as a fallback
        if [ -f "/shared-app/public/api/index.php" ]; then
          echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

          # Check if the file already starts with <?php
          if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
          fi

          echo "✅ PHP-FPM polyfill injected into API index.php"
        fi

        # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

          # Create backup
          cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
          fi

          echo "✅ PHP-FPM polyfill injected into CLI entry point"
        else
          echo "Warning: /shared-app/bin/architrave.php not found"
        fi

        # Also check for alternative CLI entry points
        if [ -f "/shared-app/bin/console.php" ]; then
          echo "Injecting PHP-FPM polyfill into console.php..."

          # Create backup
          cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

          # Check if the file already starts with <?php
          if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
            # File starts with <?php, inject after the opening tag
            sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          else
            # File doesn't start with <?php, add both
            sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
          fi

          echo "✅ PHP-FPM polyfill injected into console.php"
        fi

        # Verify config file exists
        if [ -f "/shared-app/config/application.config.php" ]; then
          echo "✅ Config file exists at /shared-app/config/application.config.php"
        else
          echo "❌ Config file missing at /shared-app/config/application.config.php"
          echo "Available files in /shared-app/config/:"
          ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
        fi

        echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-config-fixer
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

        # Test if polyfill was loaded correctly
        php -r "
        require_once '/shared-app/php_fpm_polyfill.php';

        echo 'Testing PHP-FPM polyfill functions...\n';

        // Test apache_request_headers function
        if (function_exists('apache_request_headers')) {
            echo '✅ apache_request_headers() function is available\n';
        } else {
            echo '❌ apache_request_headers() function is missing\n';
            exit(1);
        }

        // Test getallheaders function
        if (function_exists('getallheaders')) {
            echo '✅ getallheaders() function is available\n';
        } else {
            echo '❌ getallheaders() function is missing\n';
            exit(1);
        }

        // Test apache_response_headers function
        if (function_exists('apache_response_headers')) {
            echo '✅ apache_response_headers() function is available\n';
        } else {
            echo '❌ apache_response_headers() function is missing\n';
            exit(1);
        }

        echo '✅ All PHP-FPM polyfill functions are working correctly\n';
        "

        # Test basic PHP application loading
        echo "Testing PHP application bootstrap loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            echo '✅ Bootstrap loaded successfully\n';
        } catch (Exception \$e) {
            echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # Test Laminas application config loading
        echo "Testing Laminas application config loading..."
        php -d display_errors=1 -d error_reporting=E_ALL -r "
        try {
            require '/shared-app/bootstrap.php';
            \$config = require '/shared-app/config/application.config.php';
            echo '✅ Application config loaded successfully\n';
            echo 'Config modules: ' . count(\$config['modules']) . '\n';
        } catch (Exception \$e) {
            echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
            echo 'File: ' . \$e->getFile() . '\n';
            echo 'Line: ' . \$e->getLine() . '\n';
            exit(1);
        }
        "

        # CRITICAL FIX: Test CLI entry point functionality
        echo "Testing CLI entry point functionality..."
        if [ -f "/shared-app/bin/architrave.php" ]; then
          echo "Testing bin/architrave.php polyfill integration..."
          php -d display_errors=1 -d error_reporting=E_ALL -r "
          try {
              // Change to the correct directory
              chdir('/shared-app');

              // Test if polyfill file exists and is readable
              if (!file_exists('php_fpm_polyfill.php')) {
                  echo '❌ Polyfill file not found\n';
                  exit(1);
              }

              // Load polyfill manually to test
              require_once 'php_fpm_polyfill.php';

              // Test if functions are available
              if (function_exists('apache_request_headers')) {
                  echo '✅ CLI polyfill functions available\n';
              } else {
                  echo '❌ CLI polyfill functions missing\n';
                  exit(1);
              }

              echo '✅ CLI environment validation successful\n';
          } catch (Exception \$e) {
              echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
              echo 'File: ' . \$e->getFile() . '\n';
              echo 'Line: ' . \$e->getLine() . '\n';
              // Don't exit with error as this might fail in init container context
          }
          " || echo "⚠️ CLI test completed with warnings (normal in init container)"
        else
          echo "⚠️ CLI entry point not found - will be available after application sync"
        fi

        echo "✅ PHP environment validation completed successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imagePullPolicy: IfNotPresent
      name: php-env-validator
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for port 8080..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 8080;
            server_name localhost;
            root /shared-app/public;
            index index.php index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /api/health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # Root endpoint - redirect to API
            location = / {
                return 302 /api/;
            }

            location / {
                try_files $uri $uri/ /api/index.php?$args;
            }

            location ~ \.php$ {
                fastcgi_pass localhost:9000;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                fastcgi_param AUTOMATED_TEST 1;
                include fastcgi_params;
                fastcgi_read_timeout 310;
            }

            location ~ /\.ht {
                deny all;
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully"
      env:
      - name: AWS_STS_REGIONAL_ENDPOINTS
        value: regional
      - name: AWS_DEFAULT_REGION
        value: eu-central-1
      - name: AWS_REGION
        value: eu-central-1
      - name: AWS_ROLE_ARN
        value: arn:aws:iam::************:role/tenant-prodtest2025-s3-role
      - name: AWS_WEB_IDENTITY_TOKEN_FILE
        value: /var/run/secrets/eks.amazonaws.com/serviceaccount/token
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
    nodeName: ip-10-0-10-209.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: prodtest2025-s3-service-account
    serviceAccountName: prodtest2025-s3-service-account
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: aws-iam-token
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            audience: sts.amazonaws.com
            expirationSeconds: 86400
            path: token
    - emptyDir: {}
      name: s3-storage
    - emptyDir: {}
      name: ssl-certs
    - emptyDir: {}
      name: shared-app
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-kbb2d
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T13:30:56Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T13:32:28Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T13:33:31Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T13:33:31Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-30T13:30:36Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://f246c3787fac790798887365840c6459e052cd4729045121886e71b6333d08dc
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: backend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T13:32:28Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://90e70e02a30ea6902aad19891d94a6d79470e0143625fcf26ad4b2606f631e29
      image: docker.io/library/nginx:1.21-alpine
      imageID: docker.io/library/nginx@sha256:a74534e76ee1121d418fa7394ca930eb67440deda413848bc67c68138535b989
      lastState: {}
      name: nginx
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-30T13:32:29Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    initContainerStatuses:
    - containerID: containerd://ca4e6a4decb17b5b656975de02640ab31ca7654d3e0070b4eef1a59d505b3c77
      image: docker.io/amazon/aws-cli:latest
      imageID: docker.io/amazon/aws-cli@sha256:a5b482028826a2e109c53eb70c64ca70973c06265347103c7385f115aea772c7
      lastState: {}
      name: s3-sync
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://ca4e6a4decb17b5b656975de02640ab31ca7654d3e0070b4eef1a59d505b3c77
          exitCode: 0
          finishedAt: "2025-07-30T13:30:57Z"
          reason: Completed
          startedAt: "2025-07-30T13:30:55Z"
      volumeMounts:
      - mountPath: /storage
        name: s3-storage
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://8e4518732e7df319d00c0b923bf92b92c4090142061fdec9cce4f38a5163a48e
      image: docker.io/curlimages/curl:latest
      imageID: docker.io/curlimages/curl@sha256:4026b29997dc7c823b51c164b71e2b51e0fd95cce4601f78202c513d97da2922
      lastState: {}
      name: ssl-cert-downloader
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://8e4518732e7df319d00c0b923bf92b92c4090142061fdec9cce4f38a5163a48e
          exitCode: 0
          finishedAt: "2025-07-30T13:31:08Z"
          reason: Completed
          startedAt: "2025-07-30T13:31:08Z"
      volumeMounts:
      - mountPath: /tmp
        name: ssl-certs
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://773b3cd09507d11edb550f1611f793b06193b95ffa025d878bdc69bf0af99572
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: app-files-copier
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://773b3cd09507d11edb550f1611f793b06193b95ffa025d878bdc69bf0af99572
          exitCode: 0
          finishedAt: "2025-07-30T13:32:19Z"
          reason: Completed
          startedAt: "2025-07-30T13:32:12Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://32ddc09b0d6c3d333498974e560dd1a1a05293b87d565c57f846919c7b7c9727
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-config-fixer
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://32ddc09b0d6c3d333498974e560dd1a1a05293b87d565c57f846919c7b7c9727
          exitCode: 0
          finishedAt: "2025-07-30T13:32:23Z"
          reason: Completed
          startedAt: "2025-07-30T13:32:23Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://fdab56a78095348bcd9ab64404cafa485d44c58553cc1dd7772cce8cb6fe0986
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev@sha256:8bb582cf70da312d64d434b42c4e4f96edfca15bae0d4245ec423cf1d3f1b62e
      lastState: {}
      name: php-env-validator
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://fdab56a78095348bcd9ab64404cafa485d44c58553cc1dd7772cce8cb6fe0986
          exitCode: 0
          finishedAt: "2025-07-30T13:32:24Z"
          reason: Completed
          startedAt: "2025-07-30T13:32:24Z"
      volumeMounts:
      - mountPath: /shared-app
        name: shared-app
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    - containerID: containerd://cc05687772a639d67c508ea887a8973b38ee89e13a6590e38f4912f3da4d39cd
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f9a104fddb33220ec80fc45a4e606c74aadf1ef7a3832eb0b05be9e90cd61f5f
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://cc05687772a639d67c508ea887a8973b38ee89e13a6590e38f4912f3da4d39cd
          exitCode: 0
          finishedAt: "2025-07-30T13:32:27Z"
          reason: Completed
          startedAt: "2025-07-30T13:32:27Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-kbb2d
        readOnly: true
        recursiveReadOnly: Disabled
      - mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
        name: aws-iam-token
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-30T13:30:36Z"
- apiVersion: v1
  kind: Pod
  metadata:
    annotations:
      kubectl.kubernetes.io/restartedAt: "2025-07-23T22:41:00+02:00"
    creationTimestamp: "2025-07-25T21:54:53Z"
    generateName: prodtest2025-frontend-b48494d87-
    labels:
      app: prodtest2025-frontend
      component: frontend
      pod-template-hash: b48494d87
      tenant: prodtest2025
    name: prodtest2025-frontend-b48494d87-p7k4j
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: prodtest2025-frontend-b48494d87
      uid: 92935c82-61e2-4731-8002-0f6935a264e6
    resourceVersion: "5741407"
    uid: ef9c716c-79c9-4c1d-8537-fe4b2a8729c1
  spec:
    containers:
    - env:
      - name: TENANT_ID
        value: prodtest2025
      - name: DOMAIN
        value: architrave-assets.com
      - name: ENVIRONMENT
        value: production
      - name: LANGUAGE
        value: en
      - name: BACKEND_URL
        value: http://prodtest2025-backend-service:8080
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      imagePullPolicy: IfNotPresent
      livenessProbe:
        failureThreshold: 3
        httpGet:
          path: /health
          port: 80
          scheme: HTTP
        initialDelaySeconds: 30
        periodSeconds: 30
        successThreshold: 1
        timeoutSeconds: 1
      name: frontend
      ports:
      - containerPort: 80
        protocol: TCP
      readinessProbe:
        failureThreshold: 3
        httpGet:
          path: /health
          port: 80
          scheme: HTTP
        initialDelaySeconds: 10
        periodSeconds: 10
        successThreshold: 1
        timeoutSeconds: 1
      resources:
        limits:
          cpu: 200m
          memory: 128Mi
        requests:
          cpu: 50m
          memory: 64Mi
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-njrlp
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    initContainers:
    - command:
      - sh
      - -c
      - |
        echo "Creating HTTP nginx configuration for tenant prodtest2025..."
        cat > /nginx-config/default.conf << 'EOF'
        server {
            listen 80;
            server_name prodtest2025.architrave-assets.com localhost;
            root /usr/share/nginx/html;
            index index.html index.htm;

            client_max_body_size 500m;

            # Health check endpoint for Kubernetes probes
            location = /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }

            # API routes - proxy to backend service
            location /api/ {
                proxy_pass http://prodtest2025-backend-service:8080/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_connect_timeout 30s;
                proxy_send_timeout 30s;
                proxy_read_timeout 30s;
            }

            # Frontend routes - serve static files
            location / {
                try_files $uri $uri/ /index.html;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }

            # Static assets with caching
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1h;
                add_header Cache-Control "public, immutable";
            }
        }
        EOF
        echo "HTTP nginx configuration created successfully for tenant prodtest2025"
      image: busybox:latest
      imagePullPolicy: Always
      name: nginx-config-setup
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-njrlp
        readOnly: true
    nodeName: ip-10-0-12-44.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - emptyDir: {}
      name: nginx-config
    - name: kube-api-access-njrlp
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T21:54:54Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T21:54:54Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T21:55:12Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T21:55:12Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-25T21:54:53Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://860f39b56cdf4eb3848635bb6f028479c4093f45b7eee20710049d4411e54840
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev@sha256:42618102e5cc405c551c142acd857aaaa953559b63ca02fff46760e34d4ff149
      lastState: {}
      name: frontend
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-25T21:55:00Z"
      volumeMounts:
      - mountPath: /etc/nginx/conf.d
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-njrlp
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: **********
    hostIPs:
    - ip: **********
    initContainerStatuses:
    - containerID: containerd://cdda745047fc361c64a3ced1a2c7f62216b834f8a3698bd78b18f8f26ac97b00
      image: docker.io/library/busybox:latest
      imageID: docker.io/library/busybox@sha256:f85340bf132ae937d2c2a763b8335c9bab35d6e8293f70f606b9c6178d84f42b
      lastState: {}
      name: nginx-config-setup
      ready: true
      restartCount: 0
      started: false
      state:
        terminated:
          containerID: containerd://cdda745047fc361c64a3ced1a2c7f62216b834f8a3698bd78b18f8f26ac97b00
          exitCode: 0
          finishedAt: "2025-07-25T21:54:54Z"
          reason: Completed
          startedAt: "2025-07-25T21:54:54Z"
      volumeMounts:
      - mountPath: /nginx-config
        name: nginx-config
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-njrlp
        readOnly: true
        recursiveReadOnly: Disabled
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: Burstable
    startTime: "2025-07-25T21:54:53Z"
- apiVersion: v1
  kind: Pod
  metadata:
    creationTimestamp: "2025-07-23T20:38:56Z"
    generateName: prodtest2025-rabbitmq-7df8dcbcd6-
    labels:
      app: prodtest2025-rabbitmq
      component: rabbitmq
      pod-template-hash: 7df8dcbcd6
      tenant: prodtest2025
    name: prodtest2025-rabbitmq-7df8dcbcd6-ww9pk
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: ReplicaSet
      name: prodtest2025-rabbitmq-7df8dcbcd6
      uid: 0e14488c-f49e-443c-8bac-9f04fa8069a9
    resourceVersion: "4628245"
    uid: 7b14504c-7cdc-4997-ba58-d793d696f31f
  spec:
    containers:
    - env:
      - name: RABBITMQ_DEFAULT_USER
        value: guest
      - name: RABBITMQ_DEFAULT_PASS
        value: guest
      - name: TENANT_ID
        value: prodtest2025
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imagePullPolicy: IfNotPresent
      name: rabbitmq
      ports:
      - containerPort: 80
        protocol: TCP
      resources: {}
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mgrgs
        readOnly: true
    dnsPolicy: ClusterFirst
    enableServiceLinks: true
    nodeName: ip-10-0-10-127.eu-central-1.compute.internal
    preemptionPolicy: PreemptLowerPriority
    priority: 0
    restartPolicy: Always
    schedulerName: default-scheduler
    securityContext: {}
    serviceAccount: default
    serviceAccountName: default
    terminationGracePeriodSeconds: 30
    tolerations:
    - effect: NoExecute
      key: node.kubernetes.io/not-ready
      operator: Exists
      tolerationSeconds: 300
    - effect: NoExecute
      key: node.kubernetes.io/unreachable
      operator: Exists
      tolerationSeconds: 300
    volumes:
    - name: kube-api-access-mgrgs
      projected:
        defaultMode: 420
        sources:
        - serviceAccountToken:
            expirationSeconds: 3607
            path: token
        - configMap:
            items:
            - key: ca.crt
              path: ca.crt
            name: kube-root-ca.crt
        - downwardAPI:
            items:
            - fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
              path: namespace
  status:
    conditions:
    - lastProbeTime: null
      lastTransitionTime: "2025-07-23T20:38:57Z"
      status: "True"
      type: PodReadyToStartContainers
    - lastProbeTime: null
      lastTransitionTime: "2025-07-23T20:38:56Z"
      status: "True"
      type: Initialized
    - lastProbeTime: null
      lastTransitionTime: "2025-07-23T20:38:57Z"
      status: "True"
      type: Ready
    - lastProbeTime: null
      lastTransitionTime: "2025-07-23T20:38:57Z"
      status: "True"
      type: ContainersReady
    - lastProbeTime: null
      lastTransitionTime: "2025-07-23T20:38:56Z"
      status: "True"
      type: PodScheduled
    containerStatuses:
    - containerID: containerd://a32e656525b6932091672a8928f04c7b12b88700b8cc1625a1b591823781e751
      image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
      imageID: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev@sha256:a40d91656430a34f8ee2dfbac222d974915ef9d448f7e99ecb4118218a8a84c0
      lastState: {}
      name: rabbitmq
      ready: true
      restartCount: 0
      started: true
      state:
        running:
          startedAt: "2025-07-23T20:38:57Z"
      volumeMounts:
      - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
        name: kube-api-access-mgrgs
        readOnly: true
        recursiveReadOnly: Disabled
    hostIP: ***********
    hostIPs:
    - ip: ***********
    phase: Running
    podIP: ***********
    podIPs:
    - ip: ***********
    qosClass: BestEffort
    startTime: "2025-07-23T20:38:56Z"
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"prodtest2025-backend","component":"backend","tenant":"prodtest2025"},"name":"prodtest2025-backend-service","namespace":"tenant-prodtest2025"},"spec":{"ports":[{"name":"http","port":8080,"protocol":"TCP","targetPort":8080}],"selector":{"app":"prodtest2025-backend","component":"backend","tenant":"prodtest2025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-23T20:20:07Z"
    labels:
      app: prodtest2025-backend
      component: backend
      tenant: prodtest2025
    name: prodtest2025-backend-service
    namespace: tenant-prodtest2025
    resourceVersion: "4882565"
    uid: b0e8f28b-8556-41a6-a37d-b8e5002f6f73
  spec:
    clusterIP: **************
    clusterIPs:
    - **************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
    selector:
      app: prodtest2025-backend
      component: backend
      tenant: prodtest2025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"prodtest2025-frontend","component":"frontend","tenant":"prodtest2025"},"name":"prodtest2025-frontend-service","namespace":"tenant-prodtest2025"},"spec":{"ports":[{"port":80,"protocol":"TCP","targetPort":80}],"selector":{"app":"prodtest2025-frontend","component":"frontend","tenant":"prodtest2025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-23T20:20:12Z"
    labels:
      app: prodtest2025-frontend
      component: frontend
      tenant: prodtest2025
    name: prodtest2025-frontend-service
    namespace: tenant-prodtest2025
    resourceVersion: "4620325"
    uid: 185b5161-f516-4581-8edf-819274f4c9e8
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 80
      protocol: TCP
      targetPort: 80
    selector:
      app: prodtest2025-frontend
      component: frontend
      tenant: prodtest2025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"prodtest2025-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"prodtest2025"},"name":"prodtest2025-rabbitmq-mgmt-service","namespace":"tenant-prodtest2025"},"spec":{"ports":[{"port":15672,"protocol":"TCP","targetPort":15672}],"selector":{"app":"prodtest2025-rabbitmq-mgmt","component":"rabbitmq-mgmt","tenant":"prodtest2025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-23T20:17:23Z"
    labels:
      app: prodtest2025-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: prodtest2025
    name: prodtest2025-rabbitmq-mgmt-service
    namespace: tenant-prodtest2025
    resourceVersion: "4881693"
    uid: cd5bbd24-f7c3-4f7c-94d6-0ce9fb06912d
  spec:
    clusterIP: **************
    clusterIPs:
    - **************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 15672
      protocol: TCP
      targetPort: 15672
    selector:
      app: prodtest2025-rabbitmq-mgmt
      component: rabbitmq-mgmt
      tenant: prodtest2025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: v1
  kind: Service
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"v1","kind":"Service","metadata":{"annotations":{},"labels":{"app":"prodtest2025-rabbitmq","component":"rabbitmq","tenant":"prodtest2025"},"name":"prodtest2025-rabbitmq-service","namespace":"tenant-prodtest2025"},"spec":{"ports":[{"port":5672,"protocol":"TCP","targetPort":5672}],"selector":{"app":"prodtest2025-rabbitmq","component":"rabbitmq","tenant":"prodtest2025"},"type":"ClusterIP"}}
    creationTimestamp: "2025-07-23T20:17:22Z"
    labels:
      app: prodtest2025-rabbitmq
      component: rabbitmq
      tenant: prodtest2025
    name: prodtest2025-rabbitmq-service
    namespace: tenant-prodtest2025
    resourceVersion: "4619132"
    uid: d582c8f4-abb9-4eff-93c9-d3e440166ff4
  spec:
    clusterIP: *************
    clusterIPs:
    - *************
    internalTrafficPolicy: Cluster
    ipFamilies:
    - IPv4
    ipFamilyPolicy: SingleStack
    ports:
    - port: 5672
      protocol: TCP
      targetPort: 5672
    selector:
      app: prodtest2025-rabbitmq
      component: rabbitmq
      tenant: prodtest2025
    sessionAffinity: None
    type: ClusterIP
  status:
    loadBalancer: {}
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "10"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"prodtest2025-backend","component":"backend","tenant":"prodtest2025"},"name":"prodtest2025-backend","namespace":"tenant-prodtest2025"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"prodtest2025-backend","component":"backend","tenant":"prodtest2025"}},"template":{"metadata":{"labels":{"app":"prodtest2025-backend","component":"backend","tenant":"prodtest2025"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"prodtest2025"},{"name":"DB_HOST","value":"production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"},{"name":"DB_PORT","value":"3306"},{"name":"DB_NAME","value":"tenant_prodtest2025"},{"name":"DB_USER","valueFrom":{"secretKeyRef":{"key":"DB_USER","name":"prodtest2025-db-secret"}}},{"name":"DB_PASSWORD","valueFrom":{"secretKeyRef":{"key":"DB_PASSWORD","name":"prodtest2025-db-secret"}}},{"name":"DB_SSL","valueFrom":{"secretKeyRef":{"key":"DB_SSL","name":"prodtest2025-db-secret"}}},{"name":"DB_SSL_CA","valueFrom":{"secretKeyRef":{"key":"DB_SSL_CA","name":"prodtest2025-db-secret"}}},{"name":"DB_SSL_VERIFY","valueFrom":{"secretKeyRef":{"key":"DB_SSL_VERIFY","name":"prodtest2025-db-secret"}}},{"name":"RABBITMQ_URL","value":"amqp://guest:guest@prodtest2025-rabbitmq-service:5672/"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","livenessProbe":{"initialDelaySeconds":60,"periodSeconds":30,"tcpSocket":{"port":9000}},"name":"backend","ports":[{"containerPort":9000}],"readinessProbe":{"initialDelaySeconds":30,"periodSeconds":30,"tcpSocket":{"port":9000}},"resources":{"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"256Mi"}},"volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"},{"mountPath":"/shared-app","name":"shared-app"}]},{"image":"nginx:1.21-alpine","livenessProbe":{"failureThreshold":5,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-liveness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":90,"periodSeconds":60,"successThreshold":1,"timeoutSeconds":10},"name":"nginx","ports":[{"containerPort":8080}],"readinessProbe":{"failureThreshold":10,"httpGet":{"httpHeaders":[{"name":"User-Agent","value":"k8s-readiness-probe"}],"path":"/api/health","port":8080},"initialDelaySeconds":60,"periodSeconds":30,"successThreshold":1,"timeoutSeconds":5},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"},{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Downloading RDS CA certificate...\"\ncurl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem\necho \"SSL certificate downloaded successfully\"\n"],"image":"curlimages/curl:latest","name":"ssl-cert-downloader","volumeMounts":[{"mountPath":"/tmp","name":"ssl-certs"}]},{"command":["sh","-c","echo \"Copying application files from /storage/ArchAssets to shared volume...\"\nif [ -d \"/storage/ArchAssets\" ]; then\n    cp -r /storage/ArchAssets/* /shared-app/ 2\u003e/dev/null || true\n    echo \"Application files copied successfully from /storage/ArchAssets\"\n    echo \"Copied files:\"\n    ls -la /shared-app/ | head -10\nelse\n    echo \"Warning: /storage/ArchAssets directory not found in webapp_dev image\"\n    echo \"Available directories in /storage:\"\n    ls -la /storage/ 2\u003e/dev/null || echo \"No /storage directory\"\n    echo \"Available directories in root:\"\n    ls -la / | grep -E \"(var|storage|app)\" || echo \"No relevant directories found\"\nfi\necho \"Application files copy process completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"app-files-copier","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Fixing PHP application config path issues and PHP-FPM compatibility...\"\n\n# Fix the relative path issue in /shared-app/public/api/index.php\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Found API index.php, fixing config path...\"\n\n  # Create backup\n  cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup\n\n  # Fix the relative path issue by using absolute path\n  sed -i \"s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g\" /shared-app/public/api/index.php\n\n  echo \"Config path fixed in API index.php\"\n  echo \"Checking the fix:\"\n  grep -n \"require.*config.*application.config.php\" /shared-app/public/api/index.php || echo \"Pattern not found after fix\"\nelse\n  echo \"Warning: /shared-app/public/api/index.php not found\"\n  echo \"Available files in /shared-app/public/:\"\n  ls -la /shared-app/public/ 2\u003e/dev/null || echo \"No public directory\"\nfi\n\n# Create PHP-FPM compatibility polyfill for apache_request_headers()\necho \"Creating PHP-FPM compatibility polyfill...\"\ncat \u003e /shared-app/php_fpm_polyfill.php \u003c\u003c 'POLYFILL_EOF'\n\u003c?php\n/**\n * PHP-FPM Compatibility Polyfill for Apache functions\n * This file provides missing Apache functions for PHP-FPM + nginx environments\n *\n * NOTE: Using forced function definition without conditional checks\n * because function_exists() may return false positives in some PHP-FPM environments\n */\n\n/**\n * Polyfill for apache_request_headers() function in PHP-FPM environment\n * Only define if the function doesn't already exist\n */\nif (!function_exists('apache_request_headers')) {\n    function apache_request_headers() {\n        $headers = array();\n\n        // Get all HTTP headers from $_SERVER superglobal\n        foreach ($_SERVER as $key =\u003e $value) {\n            if (strpos($key, 'HTTP_') === 0) {\n                // Convert HTTP_HEADER_NAME to Header-Name format\n                $header = str_replace('_', '-', substr($key, 5));\n                $header = ucwords(strtolower($header), '-');\n                $headers[$header] = $value;\n            }\n        }\n\n        // Add special headers that might not have HTTP_ prefix\n        if (isset($_SERVER['CONTENT_TYPE'])) {\n            $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];\n        }\n        if (isset($_SERVER['CONTENT_LENGTH'])) {\n            $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];\n        }\n\n        return $headers;\n    }\n}\n\n/**\n * Polyfill for apache_response_headers() function in PHP-FPM environment\n * @return array\n */\nfunction apache_response_headers() {\n    $headers = array();\n\n    // Get headers that were set with header() function\n    if (function_exists('headers_list')) {\n        foreach (headers_list() as $header) {\n            $parts = explode(':', $header, 2);\n            if (count($parts) === 2) {\n                $headers[trim($parts[0])] = trim($parts[1]);\n            }\n        }\n    }\n\n    return $headers;\n}\n\n/**\n * Alias for apache_request_headers() - commonly used alternative\n * Only define if the function doesn't already exist\n */\nif (!function_exists('getallheaders')) {\n    function getallheaders() {\n        return apache_request_headers();\n    }\n}\nPOLYFILL_EOF\n\necho \"✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php\"\n\n# Inject the polyfill into the main bootstrap file\nif [ -f \"/shared-app/bootstrap.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into bootstrap.php...\"\n\n  # Create backup\n  cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bootstrap.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/php_fpm_polyfill.php\";' /shared-app/bootstrap.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into bootstrap.php\"\nelse\n  echo \"Warning: /shared-app/bootstrap.php not found\"\nfi\n\n# Also inject into the API index.php as a fallback\nif [ -f \"/shared-app/public/api/index.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into API index.php as fallback...\"\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/public/api/index.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../../php_fpm_polyfill.php\";' /shared-app/public/api/index.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into API index.php\"\nfi\n\n# CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)...\"\n\n  # Create backup\n  cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/architrave.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/architrave.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into CLI entry point\"\nelse\n  echo \"Warning: /shared-app/bin/architrave.php not found\"\nfi\n\n# Also check for alternative CLI entry points\nif [ -f \"/shared-app/bin/console.php\" ]; then\n  echo \"Injecting PHP-FPM polyfill into console.php...\"\n\n  # Create backup\n  cp /shared-app/bin/console.php /shared-app/bin/console.php.backup\n\n  # Check if the file already starts with \u003c?php\n  if head -1 /shared-app/bin/console.php | grep -q \"^\u003c?php\"; then\n    # File starts with \u003c?php, inject after the opening tag\n    sed -i '1a require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  else\n    # File doesn't start with \u003c?php, add both\n    sed -i '1i\u003c?php require_once __DIR__ . \"/../php_fpm_polyfill.php\";' /shared-app/bin/console.php\n  fi\n\n  echo \"✅ PHP-FPM polyfill injected into console.php\"\nfi\n\n# Verify config file exists\nif [ -f \"/shared-app/config/application.config.php\" ]; then\n  echo \"✅ Config file exists at /shared-app/config/application.config.php\"\nelse\n  echo \"❌ Config file missing at /shared-app/config/application.config.php\"\n  echo \"Available files in /shared-app/config/:\"\n  ls -la /shared-app/config/ 2\u003e/dev/null || echo \"No config directory\"\nfi\n\necho \"✅ PHP config path fix and PHP-FPM compatibility setup completed\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-config-fixer","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Validating PHP environment for PHP-FPM + nginx compatibility...\"\n\n# Test if polyfill was loaded correctly\nphp -r \"\nrequire_once '/shared-app/php_fpm_polyfill.php';\n\necho 'Testing PHP-FPM polyfill functions...\\n';\n\n// Test apache_request_headers function\nif (function_exists('apache_request_headers')) {\n    echo '✅ apache_request_headers() function is available\\n';\n} else {\n    echo '❌ apache_request_headers() function is missing\\n';\n    exit(1);\n}\n\n// Test getallheaders function\nif (function_exists('getallheaders')) {\n    echo '✅ getallheaders() function is available\\n';\n} else {\n    echo '❌ getallheaders() function is missing\\n';\n    exit(1);\n}\n\n// Test apache_response_headers function\nif (function_exists('apache_response_headers')) {\n    echo '✅ apache_response_headers() function is available\\n';\n} else {\n    echo '❌ apache_response_headers() function is missing\\n';\n    exit(1);\n}\n\necho '✅ All PHP-FPM polyfill functions are working correctly\\n';\n\"\n\n# Test basic PHP application loading\necho \"Testing PHP application bootstrap loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    echo '✅ Bootstrap loaded successfully\\n';\n} catch (Exception \\$e) {\n    echo '❌ Bootstrap loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# Test Laminas application config loading\necho \"Testing Laminas application config loading...\"\nphp -d display_errors=1 -d error_reporting=E_ALL -r \"\ntry {\n    require '/shared-app/bootstrap.php';\n    \\$config = require '/shared-app/config/application.config.php';\n    echo '✅ Application config loaded successfully\\n';\n    echo 'Config modules: ' . count(\\$config['modules']) . '\\n';\n} catch (Exception \\$e) {\n    echo '❌ Application config loading failed: ' . \\$e-\u003egetMessage() . '\\n';\n    echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n    echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n    exit(1);\n}\n\"\n\n# CRITICAL FIX: Test CLI entry point functionality\necho \"Testing CLI entry point functionality...\"\nif [ -f \"/shared-app/bin/architrave.php\" ]; then\n  echo \"Testing bin/architrave.php polyfill integration...\"\n  php -d display_errors=1 -d error_reporting=E_ALL -r \"\n  try {\n      // Change to the correct directory\n      chdir('/shared-app');\n\n      // Test if polyfill file exists and is readable\n      if (!file_exists('php_fpm_polyfill.php')) {\n          echo '❌ Polyfill file not found\\n';\n          exit(1);\n      }\n\n      // Load polyfill manually to test\n      require_once 'php_fpm_polyfill.php';\n\n      // Test if functions are available\n      if (function_exists('apache_request_headers')) {\n          echo '✅ CLI polyfill functions available\\n';\n      } else {\n          echo '❌ CLI polyfill functions missing\\n';\n          exit(1);\n      }\n\n      echo '✅ CLI environment validation successful\\n';\n  } catch (Exception \\$e) {\n      echo '⚠️ CLI test failed: ' . \\$e-\u003egetMessage() . '\\n';\n      echo 'File: ' . \\$e-\u003egetFile() . '\\n';\n      echo 'Line: ' . \\$e-\u003egetLine() . '\\n';\n      // Don't exit with error as this might fail in init container context\n  }\n  \" || echo \"⚠️ CLI test completed with warnings (normal in init container)\"\nelse\n  echo \"⚠️ CLI entry point not found - will be available after application sync\"\nfi\n\necho \"✅ PHP environment validation completed successfully\"\n"],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24","name":"php-env-validator","volumeMounts":[{"mountPath":"/shared-app","name":"shared-app"}]},{"command":["sh","-c","echo \"Creating HTTP nginx configuration for port 8080...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 8080;\n    server_name localhost;\n    root /shared-app/public;\n    index index.php index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /api/health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # Root endpoint - redirect to API\n    location = / {\n        return 302 /api/;\n    }\n\n    location / {\n        try_files $uri $uri/ /api/index.php?$args;\n    }\n\n    location ~ \\.php$ {\n        fastcgi_pass localhost:9000;\n        fastcgi_index index.php;\n        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;\n        fastcgi_param AUTOMATED_TEST 1;\n        include fastcgi_params;\n        fastcgi_read_timeout 310;\n    }\n\n    location ~ /\\.ht {\n        deny all;\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"ssl-certs"},{"emptyDir":{},"name":"shared-app"},{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-23T20:17:24Z"
    generation: 13
    labels:
      app: prodtest2025-backend
      component: backend
      tenant: prodtest2025
    name: prodtest2025-backend
    namespace: tenant-prodtest2025
    resourceVersion: "8163343"
    uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
  spec:
    progressDeadlineSeconds: 600
    replicas: 2
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        tenant: prodtest2025
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-24T09:56:01+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant prodtest2025..."
            aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: prodtest2025-s3-service-account
        serviceAccountName: prodtest2025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    conditions:
    - lastTransitionTime: "2025-07-23T20:17:24Z"
      lastUpdateTime: "2025-07-24T07:58:36Z"
      message: ReplicaSet "prodtest2025-backend-5bf9d5cb74" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-30T13:33:31Z"
      lastUpdateTime: "2025-07-30T13:33:31Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 13
    readyReplicas: 2
    replicas: 2
    updatedReplicas: 2
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "4"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"prodtest2025-frontend","component":"frontend","tenant":"prodtest2025"},"name":"prodtest2025-frontend","namespace":"tenant-prodtest2025"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"prodtest2025-frontend","component":"frontend","tenant":"prodtest2025"}},"template":{"metadata":{"labels":{"app":"prodtest2025-frontend","component":"frontend","tenant":"prodtest2025"}},"spec":{"containers":[{"env":[{"name":"TENANT_ID","value":"prodtest2025"},{"name":"DOMAIN","value":"architrave-assets.com"},{"name":"ENVIRONMENT","value":"production"},{"name":"LANGUAGE","value":"en"},{"name":"BACKEND_URL","value":"http://prodtest2025-backend-service:8080"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl","livenessProbe":{"httpGet":{"path":"/health","port":80},"initialDelaySeconds":30,"periodSeconds":30},"name":"frontend","ports":[{"containerPort":80}],"readinessProbe":{"httpGet":{"path":"/health","port":80},"initialDelaySeconds":10,"periodSeconds":10},"resources":{"limits":{"cpu":"200m","memory":"128Mi"},"requests":{"cpu":"50m","memory":"64Mi"}},"volumeMounts":[{"mountPath":"/etc/nginx/conf.d","name":"nginx-config"}]}],"initContainers":[{"command":["sh","-c","echo \"Creating HTTP nginx configuration for tenant prodtest2025...\"\ncat \u003e /nginx-config/default.conf \u003c\u003c 'EOF'\nserver {\n    listen 80;\n    server_name prodtest2025.architrave-assets.com localhost;\n    root /usr/share/nginx/html;\n    index index.html index.htm;\n\n    client_max_body_size 500m;\n\n    # Health check endpoint for Kubernetes probes\n    location = /health {\n        access_log off;\n        return 200 \"healthy\\n\";\n        add_header Content-Type text/plain;\n    }\n\n    # API routes - proxy to backend service\n    location /api/ {\n        proxy_pass http://prodtest2025-backend-service:8080/api/;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n\n    # Frontend routes - serve static files\n    location / {\n        try_files $uri $uri/ /index.html;\n        add_header Cache-Control \"no-cache, no-store, must-revalidate\";\n        add_header Pragma \"no-cache\";\n        add_header Expires \"0\";\n    }\n\n    # Static assets with caching\n    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)$ {\n        expires 1h;\n        add_header Cache-Control \"public, immutable\";\n    }\n}\nEOF\necho \"HTTP nginx configuration created successfully for tenant prodtest2025\"\n"],"image":"busybox:latest","name":"nginx-config-setup","volumeMounts":[{"mountPath":"/nginx-config","name":"nginx-config"}]}],"volumes":[{"emptyDir":{},"name":"nginx-config"}]}}}}
    creationTimestamp: "2025-07-23T20:20:11Z"
    generation: 4
    labels:
      app: prodtest2025-frontend
      component: frontend
      tenant: prodtest2025
    name: prodtest2025-frontend
    namespace: tenant-prodtest2025
    resourceVersion: "5741412"
    uid: 3c617bd2-33bf-49f7-932a-79da64a8a8b5
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: prodtest2025-frontend
        component: frontend
        tenant: prodtest2025
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:41:00+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-frontend
          component: frontend
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: prodtest2025
          - name: DOMAIN
            value: architrave-assets.com
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://prodtest2025-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant prodtest2025..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name prodtest2025.architrave-assets.com localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://prodtest2025-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant prodtest2025"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-23T20:20:11Z"
      lastUpdateTime: "2025-07-23T20:41:15Z"
      message: ReplicaSet "prodtest2025-frontend-b48494d87" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-07-25T21:55:12Z"
      lastUpdateTime: "2025-07-25T21:55:12Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    observedGeneration: 4
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "3"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"prodtest2025-rabbitmq","component":"rabbitmq","tenant":"prodtest2025"},"name":"prodtest2025-rabbitmq","namespace":"tenant-prodtest2025"},"spec":{"replicas":1,"selector":{"matchLabels":{"app":"prodtest2025-rabbitmq","component":"rabbitmq","tenant":"prodtest2025"}},"template":{"metadata":{"labels":{"app":"prodtest2025-rabbitmq","component":"rabbitmq","tenant":"prodtest2025"}},"spec":{"containers":[{"env":[{"name":"RABBITMQ_DEFAULT_USER","value":"guest"},{"name":"RABBITMQ_DEFAULT_PASS","value":"guest"},{"name":"TENANT_ID","value":"prodtest2025"}],"image":"************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02","name":"rabbitmq","ports":[{"containerPort":80}]}]}}}}
    creationTimestamp: "2025-07-23T20:17:22Z"
    generation: 3
    labels:
      app: prodtest2025-rabbitmq
      component: rabbitmq
      tenant: prodtest2025
    name: prodtest2025-rabbitmq
    namespace: tenant-prodtest2025
    resourceVersion: "4628265"
    uid: 0936f3da-b308-4dcc-b7ba-a35e695b2e9d
  spec:
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: prodtest2025-rabbitmq
        component: rabbitmq
        tenant: prodtest2025
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: prodtest2025-rabbitmq
          component: rabbitmq
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: prodtest2025
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    conditions:
    - lastTransitionTime: "2025-07-23T20:17:27Z"
      lastUpdateTime: "2025-07-23T20:17:27Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2025-07-23T20:17:22Z"
      lastUpdateTime: "2025-07-23T20:38:57Z"
      message: ReplicaSet "prodtest2025-rabbitmq-7df8dcbcd6" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 3
    readyReplicas: 1
    replicas: 1
    updatedReplicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "3"
    creationTimestamp: "2025-07-23T20:20:10Z"
    generation: 3
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 58679fdf
      tenant: prodtest2025
    name: prodtest2025-backend-58679fdf
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4620433"
    uid: cf216170-82d4-43df-8665-60e09abea350
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 58679fdf
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:20:10+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 58679fdf
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 3
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "8"
    creationTimestamp: "2025-07-23T20:40:41Z"
    generation: 4
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 5b8cc554bf
      tenant: prodtest2025
    name: prodtest2025-backend-5b8cc554bf
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4882430"
    uid: f2422e5c-4960-4694-9b95-5439270ed7ef
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 5b8cc554bf
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:40:41+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 5b8cc554bf
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant prodtest2025..."
            aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: prodtest2025-s3-service-account
        serviceAccountName: prodtest2025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 4
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "10"
    creationTimestamp: "2025-07-24T07:56:01Z"
    generation: 2
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 5bf9d5cb74
      tenant: prodtest2025
    name: prodtest2025-backend-5bf9d5cb74
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "8163342"
    uid: ed8bedd2-b44b-4f0c-ba2c-9cefa4990656
  spec:
    replicas: 2
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 5bf9d5cb74
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-24T09:56:01+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 5bf9d5cb74
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant prodtest2025..."
            aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: prodtest2025-s3-service-account
        serviceAccountName: prodtest2025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 2
    fullyLabeledReplicas: 2
    observedGeneration: 2
    readyReplicas: 2
    replicas: 2
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-23T20:17:24Z"
    generation: 2
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 6b9cbd7b55
      tenant: prodtest2025
    name: prodtest2025-backend-6b9cbd7b55
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4621090"
    uid: 23f90261-fde1-4195-8a29-a2e77c9b357a
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 6b9cbd7b55
        tenant: prodtest2025
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 6b9cbd7b55
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "4"
    creationTimestamp: "2025-07-23T20:20:20Z"
    generation: 3
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 6c896df785
      tenant: prodtest2025
    name: prodtest2025-backend-6c896df785
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4626166"
    uid: a4dc75d7-8b77-4a36-8f16-dd9da9dbeb27
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 6c896df785
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:20:10+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 6c896df785
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant prodtest2025..."
            aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: prodtest2025-s3-service-account
        serviceAccountName: prodtest2025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 3
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "6"
    creationTimestamp: "2025-07-23T20:34:22Z"
    generation: 2
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 6d6d9f6c75
      tenant: prodtest2025
    name: prodtest2025-backend-6d6d9f6c75
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4628879"
    uid: d8c9e001-e4aa-48ae-a43f-e6216c10d2dd
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 6d6d9f6c75
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:34:22+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 6d6d9f6c75
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant prodtest2025..."
            aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: prodtest2025-s3-service-account
        serviceAccountName: prodtest2025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "5"
    creationTimestamp: "2025-07-23T20:32:42Z"
    generation: 2
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 79c7584bbc
      tenant: prodtest2025
    name: prodtest2025-backend-79c7584bbc
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4626875"
    uid: a7994b63-8fce-4a68-a727-5b0af0f98b8b
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 79c7584bbc
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:32:42+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 79c7584bbc
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant prodtest2025..."
            aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: prodtest2025-s3-service-account
        serviceAccountName: prodtest2025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "2"
      deployment.kubernetes.io/max-replicas: "3"
      deployment.kubernetes.io/revision: "9"
    creationTimestamp: "2025-07-24T07:53:54Z"
    generation: 4
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 848f6dc695
      tenant: prodtest2025
    name: prodtest2025-backend-848f6dc695
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4883822"
    uid: 6cab3af3-6506-44d3-ba90-d79027d17d55
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 848f6dc695
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-24T09:53:54+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 848f6dc695
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant prodtest2025..."
            aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: prodtest2025-s3-service-account
        serviceAccountName: prodtest2025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 4
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-23T20:19:03Z"
    generation: 2
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: 86685c64fc
      tenant: prodtest2025
    name: prodtest2025-backend-86685c64fc
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4620285"
    uid: 8196a459-24aa-420c-9171-bd8158cb0231
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: 86685c64fc
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:19:03+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: 86685c64fc
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "7"
    creationTimestamp: "2025-07-23T20:39:05Z"
    generation: 2
    labels:
      app: prodtest2025-backend
      component: backend
      pod-template-hash: fdcf679d9
      tenant: prodtest2025
    name: prodtest2025-backend-fdcf679d9
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-backend
      uid: a47375b0-1dfe-4dd7-a740-71673987b5a7
    resourceVersion: "4629613"
    uid: dbf9f775-1c52-4ff8-be6a-7977b96949ce
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-backend
        component: backend
        pod-template-hash: fdcf679d9
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:39:05+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-backend
          component: backend
          pod-template-hash: fdcf679d9
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          - name: TENANT_ID
            value: prodtest2025
          - name: DB_HOST
            value: production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com
          - name: DB_PORT
            value: "3306"
          - name: DB_NAME
            value: tenant_prodtest2025
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                key: DB_USER
                name: prodtest2025-db-secret
          - name: DB_PASSWORD
            valueFrom:
              secretKeyRef:
                key: DB_PASSWORD
                name: prodtest2025-db-secret
          - name: DB_SSL
            valueFrom:
              secretKeyRef:
                key: DB_SSL
                name: prodtest2025-db-secret
          - name: DB_SSL_CA
            valueFrom:
              secretKeyRef:
                key: DB_SSL_CA
                name: prodtest2025-db-secret
          - name: DB_SSL_VERIFY
            valueFrom:
              secretKeyRef:
                key: DB_SSL_VERIFY
                name: prodtest2025-db-secret
          - name: RABBITMQ_URL
            value: amqp://guest:guest@prodtest2025-rabbitmq-service:5672/
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          name: backend
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            tcpSocket:
              port: 9000
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 256Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
          - mountPath: /tmp
            name: ssl-certs
          - mountPath: /shared-app
            name: shared-app
        - image: nginx:1.21-alpine
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 5
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-liveness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 90
            periodSeconds: 60
            successThreshold: 1
            timeoutSeconds: 10
          name: nginx
          ports:
          - containerPort: 8080
            protocol: TCP
          readinessProbe:
            failureThreshold: 10
            httpGet:
              httpHeaders:
              - name: User-Agent
                value: k8s-readiness-probe
              path: /api/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Syncing S3 content for tenant prodtest2025..."
            aws s3 sync s3://architravetestdb/prodtest2025/ /storage/ --delete || echo "S3 sync completed (some files may not exist yet)"
            echo "S3 content sync completed"
          env:
          - name: AWS_REGION
            value: eu-central-1
          - name: AWS_DEFAULT_REGION
            value: eu-central-1
          image: amazon/aws-cli:latest
          imagePullPolicy: Always
          name: s3-sync
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /storage
            name: s3-storage
        - command:
          - sh
          - -c
          - |
            echo "Downloading RDS CA certificate..."
            curl -o /tmp/rds-ca-2019-root.pem https://s3.amazonaws.com/rds-downloads/rds-ca-2019-root.pem
            echo "SSL certificate downloaded successfully"
          image: curlimages/curl:latest
          imagePullPolicy: Always
          name: ssl-cert-downloader
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /tmp
            name: ssl-certs
        - command:
          - sh
          - -c
          - |
            echo "Copying application files from /storage/ArchAssets to shared volume..."
            if [ -d "/storage/ArchAssets" ]; then
                cp -r /storage/ArchAssets/* /shared-app/ 2>/dev/null || true
                echo "Application files copied successfully from /storage/ArchAssets"
                echo "Copied files:"
                ls -la /shared-app/ | head -10
            else
                echo "Warning: /storage/ArchAssets directory not found in webapp_dev image"
                echo "Available directories in /storage:"
                ls -la /storage/ 2>/dev/null || echo "No /storage directory"
                echo "Available directories in root:"
                ls -la / | grep -E "(var|storage|app)" || echo "No relevant directories found"
            fi
            echo "Application files copy process completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: app-files-copier
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Fixing PHP application config path issues and PHP-FPM compatibility..."

            # Fix the relative path issue in /shared-app/public/api/index.php
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Found API index.php, fixing config path..."

              # Create backup
              cp /shared-app/public/api/index.php /shared-app/public/api/index.php.backup

              # Fix the relative path issue by using absolute path
              sed -i "s|require 'config/application.config.php'|require __DIR__ . '/../../config/application.config.php'|g" /shared-app/public/api/index.php

              echo "Config path fixed in API index.php"
              echo "Checking the fix:"
              grep -n "require.*config.*application.config.php" /shared-app/public/api/index.php || echo "Pattern not found after fix"
            else
              echo "Warning: /shared-app/public/api/index.php not found"
              echo "Available files in /shared-app/public/:"
              ls -la /shared-app/public/ 2>/dev/null || echo "No public directory"
            fi

            # Create PHP-FPM compatibility polyfill for apache_request_headers()
            echo "Creating PHP-FPM compatibility polyfill..."
            cat > /shared-app/php_fpm_polyfill.php << 'POLYFILL_EOF'
            <?php
            /**
             * PHP-FPM Compatibility Polyfill for Apache functions
             * This file provides missing Apache functions for PHP-FPM + nginx environments
             *
             * NOTE: Using forced function definition without conditional checks
             * because function_exists() may return false positives in some PHP-FPM environments
             */

            /**
             * Polyfill for apache_request_headers() function in PHP-FPM environment
             * Only define if the function doesn't already exist
             */
            if (!function_exists('apache_request_headers')) {
                function apache_request_headers() {
                    $headers = array();

                    // Get all HTTP headers from $_SERVER superglobal
                    foreach ($_SERVER as $key => $value) {
                        if (strpos($key, 'HTTP_') === 0) {
                            // Convert HTTP_HEADER_NAME to Header-Name format
                            $header = str_replace('_', '-', substr($key, 5));
                            $header = ucwords(strtolower($header), '-');
                            $headers[$header] = $value;
                        }
                    }

                    // Add special headers that might not have HTTP_ prefix
                    if (isset($_SERVER['CONTENT_TYPE'])) {
                        $headers['Content-Type'] = $_SERVER['CONTENT_TYPE'];
                    }
                    if (isset($_SERVER['CONTENT_LENGTH'])) {
                        $headers['Content-Length'] = $_SERVER['CONTENT_LENGTH'];
                    }

                    return $headers;
                }
            }

            /**
             * Polyfill for apache_response_headers() function in PHP-FPM environment
             * @return array
             */
            function apache_response_headers() {
                $headers = array();

                // Get headers that were set with header() function
                if (function_exists('headers_list')) {
                    foreach (headers_list() as $header) {
                        $parts = explode(':', $header, 2);
                        if (count($parts) === 2) {
                            $headers[trim($parts[0])] = trim($parts[1]);
                        }
                    }
                }

                return $headers;
            }

            /**
             * Alias for apache_request_headers() - commonly used alternative
             * Only define if the function doesn't already exist
             */
            if (!function_exists('getallheaders')) {
                function getallheaders() {
                    return apache_request_headers();
                }
            }
            POLYFILL_EOF

            echo "✅ PHP-FPM polyfill created at /shared-app/php_fpm_polyfill.php"

            # Inject the polyfill into the main bootstrap file
            if [ -f "/shared-app/bootstrap.php" ]; then
              echo "Injecting PHP-FPM polyfill into bootstrap.php..."

              # Create backup
              cp /shared-app/bootstrap.php /shared-app/bootstrap.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bootstrap.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/php_fpm_polyfill.php";' /shared-app/bootstrap.php
              fi

              echo "✅ PHP-FPM polyfill injected into bootstrap.php"
            else
              echo "Warning: /shared-app/bootstrap.php not found"
            fi

            # Also inject into the API index.php as a fallback
            if [ -f "/shared-app/public/api/index.php" ]; then
              echo "Injecting PHP-FPM polyfill into API index.php as fallback..."

              # Check if the file already starts with <?php
              if head -1 /shared-app/public/api/index.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../../php_fpm_polyfill.php";' /shared-app/public/api/index.php
              fi

              echo "✅ PHP-FPM polyfill injected into API index.php"
            fi

            # CRITICAL FIX: Inject polyfill into CLI entry point (bin/architrave.php)
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Injecting PHP-FPM polyfill into CLI entry point (bin/architrave.php)..."

              # Create backup
              cp /shared-app/bin/architrave.php /shared-app/bin/architrave.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/architrave.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/architrave.php
              fi

              echo "✅ PHP-FPM polyfill injected into CLI entry point"
            else
              echo "Warning: /shared-app/bin/architrave.php not found"
            fi

            # Also check for alternative CLI entry points
            if [ -f "/shared-app/bin/console.php" ]; then
              echo "Injecting PHP-FPM polyfill into console.php..."

              # Create backup
              cp /shared-app/bin/console.php /shared-app/bin/console.php.backup

              # Check if the file already starts with <?php
              if head -1 /shared-app/bin/console.php | grep -q "^<?php"; then
                # File starts with <?php, inject after the opening tag
                sed -i '1a require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              else
                # File doesn't start with <?php, add both
                sed -i '1i<?php require_once __DIR__ . "/../php_fpm_polyfill.php";' /shared-app/bin/console.php
              fi

              echo "✅ PHP-FPM polyfill injected into console.php"
            fi

            # Verify config file exists
            if [ -f "/shared-app/config/application.config.php" ]; then
              echo "✅ Config file exists at /shared-app/config/application.config.php"
            else
              echo "❌ Config file missing at /shared-app/config/application.config.php"
              echo "Available files in /shared-app/config/:"
              ls -la /shared-app/config/ 2>/dev/null || echo "No config directory"
            fi

            echo "✅ PHP config path fix and PHP-FPM compatibility setup completed"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-config-fixer
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Validating PHP environment for PHP-FPM + nginx compatibility..."

            # Test if polyfill was loaded correctly
            php -r "
            require_once '/shared-app/php_fpm_polyfill.php';

            echo 'Testing PHP-FPM polyfill functions...\n';

            // Test apache_request_headers function
            if (function_exists('apache_request_headers')) {
                echo '✅ apache_request_headers() function is available\n';
            } else {
                echo '❌ apache_request_headers() function is missing\n';
                exit(1);
            }

            // Test getallheaders function
            if (function_exists('getallheaders')) {
                echo '✅ getallheaders() function is available\n';
            } else {
                echo '❌ getallheaders() function is missing\n';
                exit(1);
            }

            // Test apache_response_headers function
            if (function_exists('apache_response_headers')) {
                echo '✅ apache_response_headers() function is available\n';
            } else {
                echo '❌ apache_response_headers() function is missing\n';
                exit(1);
            }

            echo '✅ All PHP-FPM polyfill functions are working correctly\n';
            "

            # Test basic PHP application loading
            echo "Testing PHP application bootstrap loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                echo '✅ Bootstrap loaded successfully\n';
            } catch (Exception \$e) {
                echo '❌ Bootstrap loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # Test Laminas application config loading
            echo "Testing Laminas application config loading..."
            php -d display_errors=1 -d error_reporting=E_ALL -r "
            try {
                require '/shared-app/bootstrap.php';
                \$config = require '/shared-app/config/application.config.php';
                echo '✅ Application config loaded successfully\n';
                echo 'Config modules: ' . count(\$config['modules']) . '\n';
            } catch (Exception \$e) {
                echo '❌ Application config loading failed: ' . \$e->getMessage() . '\n';
                echo 'File: ' . \$e->getFile() . '\n';
                echo 'Line: ' . \$e->getLine() . '\n';
                exit(1);
            }
            "

            # CRITICAL FIX: Test CLI entry point functionality
            echo "Testing CLI entry point functionality..."
            if [ -f "/shared-app/bin/architrave.php" ]; then
              echo "Testing bin/architrave.php polyfill integration..."
              php -d display_errors=1 -d error_reporting=E_ALL -r "
              try {
                  // Change to the correct directory
                  chdir('/shared-app');

                  // Test if polyfill file exists and is readable
                  if (!file_exists('php_fpm_polyfill.php')) {
                      echo '❌ Polyfill file not found\n';
                      exit(1);
                  }

                  // Load polyfill manually to test
                  require_once 'php_fpm_polyfill.php';

                  // Test if functions are available
                  if (function_exists('apache_request_headers')) {
                      echo '✅ CLI polyfill functions available\n';
                  } else {
                      echo '❌ CLI polyfill functions missing\n';
                      exit(1);
                  }

                  echo '✅ CLI environment validation successful\n';
              } catch (Exception \$e) {
                  echo '⚠️ CLI test failed: ' . \$e->getMessage() . '\n';
                  echo 'File: ' . \$e->getFile() . '\n';
                  echo 'Line: ' . \$e->getLine() . '\n';
                  // Don't exit with error as this might fail in init container context
              }
              " || echo "⚠️ CLI test completed with warnings (normal in init container)"
            else
              echo "⚠️ CLI entry point not found - will be available after application sync"
            fi

            echo "✅ PHP environment validation completed successfully"
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:multistage-8.1-fpm-bookworm_v24
          imagePullPolicy: IfNotPresent
          name: php-env-validator
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /shared-app
            name: shared-app
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for port 8080..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 8080;
                server_name localhost;
                root /shared-app/public;
                index index.php index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /api/health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # Root endpoint - redirect to API
                location = / {
                    return 302 /api/;
                }

                location / {
                    try_files $uri $uri/ /api/index.php?$args;
                }

                location ~ \.php$ {
                    fastcgi_pass localhost:9000;
                    fastcgi_index index.php;
                    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                    fastcgi_param AUTOMATED_TEST 1;
                    include fastcgi_params;
                    fastcgi_read_timeout 310;
                }

                location ~ /\.ht {
                    deny all;
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: prodtest2025-s3-service-account
        serviceAccountName: prodtest2025-s3-service-account
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: s3-storage
        - emptyDir: {}
          name: ssl-certs
        - emptyDir: {}
          name: shared-app
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "1"
    creationTimestamp: "2025-07-23T20:20:11Z"
    generation: 2
    labels:
      app: prodtest2025-frontend
      component: frontend
      pod-template-hash: 5b898dfbff
      tenant: prodtest2025
    name: prodtest2025-frontend-5b898dfbff
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-frontend
      uid: 3c617bd2-33bf-49f7-932a-79da64a8a8b5
    resourceVersion: "4620752"
    uid: 6b5e0f1f-21fa-4e29-a8a3-49861f08c132
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-frontend
        component: frontend
        pod-template-hash: 5b898dfbff
        tenant: prodtest2025
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: prodtest2025-frontend
          component: frontend
          pod-template-hash: 5b898dfbff
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: prodtest2025
          - name: DOMAIN
            value: architrave-assets.com
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://prodtest2025-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant prodtest2025..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name prodtest2025.architrave-assets.com localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://prodtest2025-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant prodtest2025"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "3"
    creationTimestamp: "2025-07-23T20:34:42Z"
    generation: 2
    labels:
      app: prodtest2025-frontend
      component: frontend
      pod-template-hash: 6d4b6975c
      tenant: prodtest2025
    name: prodtest2025-frontend-6d4b6975c
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-frontend
      uid: 3c617bd2-33bf-49f7-932a-79da64a8a8b5
    resourceVersion: "4629321"
    uid: fa4e4074-0a9c-4c7b-9d84-a3ec7df9021f
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-frontend
        component: frontend
        pod-template-hash: 6d4b6975c
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:34:42+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-frontend
          component: frontend
          pod-template-hash: 6d4b6975c
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: prodtest2025
          - name: DOMAIN
            value: architrave-assets.com
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://prodtest2025-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant prodtest2025..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name prodtest2025.architrave-assets.com localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://prodtest2025-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant prodtest2025"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-23T20:20:32Z"
    generation: 2
    labels:
      app: prodtest2025-frontend
      component: frontend
      pod-template-hash: 7d95b75d9
      tenant: prodtest2025
    name: prodtest2025-frontend-7d95b75d9
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-frontend
      uid: 3c617bd2-33bf-49f7-932a-79da64a8a8b5
    resourceVersion: "4626578"
    uid: 74113751-51ca-48eb-83ff-8efdd53a4bea
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-frontend
        component: frontend
        pod-template-hash: 7d95b75d9
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:20:32+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-frontend
          component: frontend
          pod-template-hash: 7d95b75d9
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: prodtest2025
          - name: DOMAIN
            value: architrave-assets.com
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://prodtest2025-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant prodtest2025..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name prodtest2025.architrave-assets.com localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://prodtest2025-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant prodtest2025"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "4"
    creationTimestamp: "2025-07-23T20:41:00Z"
    generation: 1
    labels:
      app: prodtest2025-frontend
      component: frontend
      pod-template-hash: b48494d87
      tenant: prodtest2025
    name: prodtest2025-frontend-b48494d87
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-frontend
      uid: 3c617bd2-33bf-49f7-932a-79da64a8a8b5
    resourceVersion: "5741411"
    uid: 92935c82-61e2-4731-8002-0f6935a264e6
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: prodtest2025-frontend
        component: frontend
        pod-template-hash: b48494d87
        tenant: prodtest2025
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2025-07-23T22:41:00+02:00"
        creationTimestamp: null
        labels:
          app: prodtest2025-frontend
          component: frontend
          pod-template-hash: b48494d87
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: TENANT_ID
            value: prodtest2025
          - name: DOMAIN
            value: architrave-assets.com
          - name: ENVIRONMENT
            value: production
          - name: LANGUAGE
            value: en
          - name: BACKEND_URL
            value: http://prodtest2025-backend-service:8080
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 1
          name: frontend
          ports:
          - containerPort: 80
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: 200m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/nginx/conf.d
            name: nginx-config
        dnsPolicy: ClusterFirst
        initContainers:
        - command:
          - sh
          - -c
          - |
            echo "Creating HTTP nginx configuration for tenant prodtest2025..."
            cat > /nginx-config/default.conf << 'EOF'
            server {
                listen 80;
                server_name prodtest2025.architrave-assets.com localhost;
                root /usr/share/nginx/html;
                index index.html index.htm;

                client_max_body_size 500m;

                # Health check endpoint for Kubernetes probes
                location = /health {
                    access_log off;
                    return 200 "healthy\n";
                    add_header Content-Type text/plain;
                }

                # API routes - proxy to backend service
                location /api/ {
                    proxy_pass http://prodtest2025-backend-service:8080/api/;
                    proxy_set_header Host $host;
                    proxy_set_header X-Real-IP $remote_addr;
                    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header X-Forwarded-Proto $scheme;
                    proxy_connect_timeout 30s;
                    proxy_send_timeout 30s;
                    proxy_read_timeout 30s;
                }

                # Frontend routes - serve static files
                location / {
                    try_files $uri $uri/ /index.html;
                    add_header Cache-Control "no-cache, no-store, must-revalidate";
                    add_header Pragma "no-cache";
                    add_header Expires "0";
                }

                # Static assets with caching
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                    expires 1h;
                    add_header Cache-Control "public, immutable";
                }
            }
            EOF
            echo "HTTP nginx configuration created successfully for tenant prodtest2025"
          image: busybox:latest
          imagePullPolicy: Always
          name: nginx-config-setup
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /nginx-config
            name: nginx-config
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
        volumes:
        - emptyDir: {}
          name: nginx-config
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 1
    readyReplicas: 1
    replicas: 1
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "2"
    creationTimestamp: "2025-07-23T20:32:34Z"
    generation: 2
    labels:
      app: prodtest2025-rabbitmq
      component: rabbitmq
      pod-template-hash: 7695f7f9cb
      tenant: prodtest2025
    name: prodtest2025-rabbitmq-7695f7f9cb
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-rabbitmq
      uid: 0936f3da-b308-4dcc-b7ba-a35e695b2e9d
    resourceVersion: "4628264"
    uid: 539f891f-2f77-4a9d-b5a9-c4c524840523
  spec:
    replicas: 0
    selector:
      matchLabels:
        app: prodtest2025-rabbitmq
        component: rabbitmq
        pod-template-hash: 7695f7f9cb
        tenant: prodtest2025
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: prodtest2025-rabbitmq
          component: rabbitmq
          pod-template-hash: 7695f7f9cb
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: prodtest2025
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    observedGeneration: 2
    replicas: 0
- apiVersion: apps/v1
  kind: ReplicaSet
  metadata:
    annotations:
      deployment.kubernetes.io/desired-replicas: "1"
      deployment.kubernetes.io/max-replicas: "2"
      deployment.kubernetes.io/revision: "3"
      deployment.kubernetes.io/revision-history: "1"
    creationTimestamp: "2025-07-23T20:17:22Z"
    generation: 3
    labels:
      app: prodtest2025-rabbitmq
      component: rabbitmq
      pod-template-hash: 7df8dcbcd6
      tenant: prodtest2025
    name: prodtest2025-rabbitmq-7df8dcbcd6
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: prodtest2025-rabbitmq
      uid: 0936f3da-b308-4dcc-b7ba-a35e695b2e9d
    resourceVersion: "4628246"
    uid: 0e14488c-f49e-443c-8bac-9f04fa8069a9
  spec:
    replicas: 1
    selector:
      matchLabels:
        app: prodtest2025-rabbitmq
        component: rabbitmq
        pod-template-hash: 7df8dcbcd6
        tenant: prodtest2025
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: prodtest2025-rabbitmq
          component: rabbitmq
          pod-template-hash: 7df8dcbcd6
          tenant: prodtest2025
      spec:
        containers:
        - env:
          - name: RABBITMQ_DEFAULT_USER
            value: guest
          - name: RABBITMQ_DEFAULT_PASS
            value: guest
          - name: TENANT_ID
            value: prodtest2025
          image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
          imagePullPolicy: IfNotPresent
          name: rabbitmq
          ports:
          - containerPort: 80
            protocol: TCP
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 1
    fullyLabeledReplicas: 1
    observedGeneration: 3
    readyReplicas: 1
    replicas: 1
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"keda.sh/v1alpha1","kind":"ScaledObject","metadata":{"annotations":{},"labels":{"app":"prodtest2025-backend","tenant":"prodtest2025"},"name":"prodtest2025-backend-scaler","namespace":"tenant-prodtest2025"},"spec":{"advanced":{"horizontalPodAutoscalerConfig":{"behavior":{"scaleDown":{"policies":[{"periodSeconds":60,"type":"Percent","value":50}],"stabilizationWindowSeconds":300},"scaleUp":{"policies":[{"periodSeconds":30,"type":"Percent","value":100},{"periodSeconds":30,"type":"Pods","value":2}],"selectPolicy":"Max","stabilizationWindowSeconds":0}}}},"cooldownPeriod":300,"maxReplicaCount":20,"minReplicaCount":2,"pollingInterval":15,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"prodtest2025-backend"},"triggers":[{"metadata":{"value":"70"},"metricType":"Utilization","type":"cpu"},{"metadata":{"value":"80"},"metricType":"Utilization","type":"memory"},{"metadata":{"metricName":"http_requests_per_second","query":"sum(rate(istio_requests_total{destination_service_name=\"prodtest2025-backend\"}[2m]))","serverAddress":"http://prometheus-server.monitoring.svc.cluster.local:9090","threshold":"50"},"type":"prometheus"},{"metadata":{"host":"prodtest2025-rabbitmq.tenant-prodtest2025.svc.cluster.local","mode":"QueueLength","password":"guest","port":"15672","protocol":"http","queueName":"tenant-queue","username":"guest","value":"5"},"type":"rabbitmq"}]}}
    creationTimestamp: "2025-07-23T20:43:24Z"
    labels:
      app: prodtest2025-backend
      app.kubernetes.io/managed-by: keda-operator
      app.kubernetes.io/name: keda-hpa-prodtest2025-backend-scaler
      app.kubernetes.io/part-of: prodtest2025-backend-scaler
      app.kubernetes.io/version: 2.17.2
      scaledobject.keda.sh/name: prodtest2025-backend-scaler
      tenant: prodtest2025
    name: keda-hpa-prodtest2025-backend-scaler
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: keda.sh/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: ScaledObject
      name: prodtest2025-backend-scaler
      uid: 08cd64b4-3556-46aa-9be2-a134007071d1
    resourceVersion: "8603340"
    uid: d2d4f125-7e7f-4bce-a8a1-69f3c0662ce0
  spec:
    behavior:
      scaleDown:
        policies:
        - periodSeconds: 60
          type: Percent
          value: 50
        selectPolicy: Max
        stabilizationWindowSeconds: 300
      scaleUp:
        policies:
        - periodSeconds: 30
          type: Percent
          value: 100
        - periodSeconds: 30
          type: Pods
          value: 2
        selectPolicy: Max
        stabilizationWindowSeconds: 0
    maxReplicas: 20
    metrics:
    - external:
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: prodtest2025-backend-scaler
        target:
          averageValue: "50"
          type: AverageValue
      type: External
    - external:
        metric:
          name: s3-rabbitmq-tenant-queue
          selector:
            matchLabels:
              scaledobject.keda.sh/name: prodtest2025-backend-scaler
        target:
          averageValue: "5"
          type: AverageValue
      type: External
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 2
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: prodtest2025-backend
  status:
    conditions:
    - lastTransitionTime: "2025-07-23T20:43:39Z"
      message: recommended size matches current size
      reason: ReadyForNewScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-25T13:36:12Z"
      message: the HPA was able to successfully calculate a replica count from cpu
        resource utilization (percentage of request)
      reason: ValidMetricFound
      status: "True"
      type: ScalingActive
    - lastTransitionTime: "2025-07-23T20:43:54Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
    currentMetrics:
    - external:
        current:
          averageValue: "0"
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: prodtest2025-backend-scaler
      type: External
    - type: ""
    - resource:
        current:
          averageUtilization: 1
          averageValue: 2m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 4
          averageValue: "16390144"
        name: memory
      type: Resource
    currentReplicas: 2
    desiredReplicas: 2
    lastScaleTime: "2025-07-24T07:54:02Z"
- apiVersion: autoscaling/v2
  kind: HorizontalPodAutoscaler
  metadata:
    annotations:
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"keda.sh/v1alpha1","kind":"ScaledObject","metadata":{"annotations":{},"labels":{"app":"prodtest2025-frontend","tenant":"prodtest2025"},"name":"prodtest2025-frontend-scaler","namespace":"tenant-prodtest2025"},"spec":{"cooldownPeriod":300,"maxReplicaCount":10,"minReplicaCount":1,"pollingInterval":15,"scaleTargetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"prodtest2025-frontend"},"triggers":[{"metadata":{"value":"70"},"metricType":"Utilization","type":"cpu"},{"metadata":{"value":"80"},"metricType":"Utilization","type":"memory"},{"metadata":{"metricName":"http_requests_per_second","query":"sum(rate(istio_requests_total{destination_service_name=\"prodtest2025-frontend\"}[2m]))","serverAddress":"http://prometheus-server.monitoring.svc.cluster.local:9090","threshold":"30"},"type":"prometheus"}]}}
    creationTimestamp: "2025-07-23T20:43:25Z"
    labels:
      app: prodtest2025-frontend
      app.kubernetes.io/managed-by: keda-operator
      app.kubernetes.io/name: keda-hpa-prodtest2025-frontend-scaler
      app.kubernetes.io/part-of: prodtest2025-frontend-scaler
      app.kubernetes.io/version: 2.17.2
      scaledobject.keda.sh/name: prodtest2025-frontend-scaler
      tenant: prodtest2025
    name: keda-hpa-prodtest2025-frontend-scaler
    namespace: tenant-prodtest2025
    ownerReferences:
    - apiVersion: keda.sh/v1alpha1
      blockOwnerDeletion: true
      controller: true
      kind: ScaledObject
      name: prodtest2025-frontend-scaler
      uid: a102f89b-4ec5-406e-8863-9ff8e31f716b
    resourceVersion: "8545261"
    uid: 75695980-d53f-4acd-8823-14722332b0f4
  spec:
    maxReplicas: 10
    metrics:
    - external:
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: prodtest2025-frontend-scaler
        target:
          averageValue: "30"
          type: AverageValue
      type: External
    - resource:
        name: cpu
        target:
          averageUtilization: 70
          type: Utilization
      type: Resource
    - resource:
        name: memory
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
    minReplicas: 1
    scaleTargetRef:
      apiVersion: apps/v1
      kind: Deployment
      name: prodtest2025-frontend
  status:
    conditions:
    - lastTransitionTime: "2025-07-23T20:43:40Z"
      message: recommended size matches current size
      reason: ReadyForNewScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-07-25T21:55:26Z"
      message: the HPA was able to successfully calculate a replica count from cpu
        resource utilization (percentage of request)
      reason: ValidMetricFound
      status: "True"
      type: ScalingActive
    - lastTransitionTime: "2025-07-23T20:43:40Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
    currentMetrics:
    - external:
        current:
          averageValue: "0"
        metric:
          name: s2-prometheus
          selector:
            matchLabels:
              scaledobject.keda.sh/name: prodtest2025-frontend-scaler
      type: External
    - resource:
        current:
          averageUtilization: 2
          averageValue: 1m
        name: cpu
      type: Resource
    - resource:
        current:
          averageUtilization: 4
          averageValue: "3244032"
        name: memory
      type: Resource
    currentReplicas: 1
    desiredReplicas: 1
kind: List
metadata:
  resourceVersion: ""
